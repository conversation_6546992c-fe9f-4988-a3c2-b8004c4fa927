#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
东方棘市小程序自动化工具 - 精简环境变量版本
作者: Tianxx
版本: v2.0.0 (精简版)
创建日期: 2025-07-30

环境变量配置说明:
DFJS_SERVER     - 微信中间服务器地址 (必需)
DFJS_WXID       - 微信ID，多账号用&分隔 (必需)
DFJS_APPID      - 小程序AppID (可选，默认: wxebdf2c44a2a714c2)
DFJS_DEBUG      - 调试模式 (可选，true/false，默认: false)
DFJS_WXPUSH_TOKEN - 微信推送Token (可选)
DFJS_WXPUSH_UID   - 微信推送UID (可选)
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from typing import Dict, Optional, Any
import requests

# 东方棘市 API常量
YS_BASE_URL = "https://ys.shajixueyuan.com"
API_MNP_LOGIN = "/api/login/mnpLogin"
API_USER_SIGN_IN = "/api/user_sign/sign"
API_USER_INFO = "/api/user/info"
API_WITHDRAW_APPLY = "/api/user.user_withdraw/apply"

# 环境变量配置
DFJS_SERVER = os.environ.get('DFJS_SERVER')
DFJS_WXID = os.environ.get('DFJS_WXID')
DFJS_APPID = os.environ.get('DFJS_APPID', 'wxebdf2c44a2a714c2')
DFJS_DEBUG = os.environ.get('DFJS_DEBUG', '').lower() == 'true'
DFJS_WXPUSH_TOKEN = os.environ.get('DFJS_WXPUSH_TOKEN')
DFJS_WXPUSH_UID = os.environ.get('DFJS_WXPUSH_UID')

# 设置日志
logging.basicConfig(
    level=logging.DEBUG if DFJS_DEBUG else logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DFJSClient:
    """东方棘市客户端 - 精简版"""
    
    def __init__(self, server: str, wxid: str, appid: str = DFJS_APPID):
        self.server = server
        self.wxid = wxid
        self.appid = appid
        self.app_token = None
        self.token_file = 'dfjs_tokens.json'
    
    def _request_post(self, url: str, data: dict, headers: dict = None) -> Optional[dict]:
        """发送POST请求"""
        try:
            if not url.startswith('http'):
                if url.startswith('/api'):
                    url = f"http://{self.server}{url}"
                else:
                    url = f"{YS_BASE_URL}{url}"
            
            logger.debug(f"POST请求: {url}")
            logger.debug(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
            
            response = requests.post(url, json=data, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()
            
            logger.debug(f"响应结果: {json.dumps(result, ensure_ascii=False)}")
            return result
        except Exception as e:
            logger.error(f"请求失败: {str(e)}")
            return None
    
    def _request_get(self, url: str, params: dict = None, headers: dict = None) -> Optional[dict]:
        """发送GET请求"""
        try:
            if not url.startswith('http'):
                url = f"{YS_BASE_URL}{url}"
            
            logger.debug(f"GET请求: {url}")
            logger.debug(f"请求参数: {params}")
            
            response = requests.get(url, params=params, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()
            
            logger.debug(f"响应结果: {json.dumps(result, ensure_ascii=False)}")
            return result
        except Exception as e:
            logger.error(f"请求失败: {str(e)}")
            return None
    
    def _get_headers(self, with_token: bool = False) -> dict:
        """获取请求头"""
        headers = {
            'accept': 'application/json',
            'content-type': 'application/json',
            'Referer': f'https://servicewechat.com/{self.appid}/163/page-frame.html',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/9129'
        }
        if with_token and self.app_token:
            headers['token'] = self.app_token
            headers['x-token'] = self.app_token
        return headers
    
    def get_wx_code(self) -> Optional[str]:
        """获取微信登录code"""
        logger.info('获取微信登录code...')
        
        result = self._request_post('/api/Wxapp/JSLogin', {
            "Appid": self.appid,
            "Wxid": self.wxid
        })
        
        if result and result.get('Success'):
            code = result.get('Data', {}).get('code')
            if code:
                logger.info('微信登录code获取成功')
                return code
        
        logger.error('微信登录code获取失败')
        return None
    
    def get_phone_code(self) -> Optional[str]:
        """获取手机授权code"""
        logger.info('获取手机授权code...')
        
        result = self._request_post('/api/Wxapp/GetAllMobile', {
            "Appid": self.appid,
            "Wxid": self.wxid
        })
        
        if result and result.get('Success'):
            mobile_list = result.get('Data', {}).get('ALLMobile', [])
            if mobile_list and len(mobile_list) > 0:
                phone_code = mobile_list[0].get('code')
                if phone_code:
                    logger.info('手机授权code获取成功')
                    return phone_code
        
        logger.error('手机授权code获取失败')
        return None
    
    def login_dfjs(self, wx_code: str, phone_code: str) -> bool:
        """登录东方棘市"""
        logger.info('登录东方棘市...')
        
        data = {
            "code": wx_code,
            "phone_code": phone_code,
            "appid": self.appid
        }
        
        result = self._request_post(API_MNP_LOGIN, data, self._get_headers())
        
        if result and result.get('code') == 1:
            self.app_token = result.get('data', {}).get('token')
            if self.app_token:
                logger.info('东方棘市登录成功')
                self._save_token()
                return True
        
        logger.error(f'东方棘市登录失败: {result.get("msg", "未知错误") if result else "请求失败"}')
        return False
    
    def sign_in(self) -> bool:
        """每日签到"""
        logger.info('执行每日签到...')
        
        data = {"token": self.app_token}
        result = self._request_post(API_USER_SIGN_IN, data, self._get_headers(True))
        
        if result and result.get('code') == 0:
            logger.info(f'签到成功: {result.get("msg", "操作成功")}')
            return True
        else:
            logger.warning(f'签到失败: {result.get("msg", "未知错误") if result else "请求失败"}')
            return False
    
    def get_user_info(self) -> Optional[dict]:
        """获取用户信息"""
        logger.info('获取用户信息...')
        
        params = {"token": self.app_token}
        result = self._request_get(API_USER_INFO, params, self._get_headers(True))
        
        if result and result.get('code') == 1:
            user_data = result.get('data')
            if user_data:
                remaining_fruits = user_data.get('remaining_fruits', '0')
                logger.info(f'用户信息获取成功，剩余水果: {remaining_fruits}')
                return user_data
        
        logger.error('用户信息获取失败')
        return None
    
    def withdraw(self, amount: str = "0.3") -> bool:
        """提现水果"""
        logger.info(f'尝试提现水果: {amount}')
        
        data = {
            "fruit_withdraw_amount": amount,
            "pay_gateway": "wechat"
        }
        
        result = self._request_post(API_WITHDRAW_APPLY, data, self._get_headers(True))
        
        if result and result.get('code') == 1:
            logger.info(f'提现成功: {result.get("msg", "操作成功")}')
            return True
        else:
            msg = result.get('msg', '未知错误') if result else '请求失败'
            if '余额不足' in msg:
                logger.info(f'提现失败: {msg}')
            else:
                logger.error(f'提现失败: {msg}')
            return False
    
    def _save_token(self):
        """保存token"""
        try:
            tokens = {}
            if os.path.exists(self.token_file):
                with open(self.token_file, 'r', encoding='utf-8') as f:
                    tokens = json.load(f)
            
            tokens[self.wxid] = {
                "app_token": self.app_token,
                "appid": self.appid,
                "timestamp": int(time.time()),
                "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(tokens, f, ensure_ascii=False, indent=2)
            
            logger.debug('Token保存成功')
        except Exception as e:
            logger.error(f'Token保存失败: {str(e)}')
    
    def run(self) -> bool:
        """执行完整流程"""
        logger.info(f'开始处理账号: {self.wxid}')
        
        # 1. 获取微信登录code
        wx_code = self.get_wx_code()
        if not wx_code:
            return False
        
        # 2. 获取手机授权code
        phone_code = self.get_phone_code()
        if not phone_code:
            return False
        
        # 3. 登录东方棘市
        if not self.login_dfjs(wx_code, phone_code):
            return False
        
        # 4. 签到
        self.sign_in()
        
        # 5. 获取用户信息并尝试提现
        user_info = self.get_user_info()
        if user_info:
            remaining_fruits = user_info.get('remaining_fruits', '0')
            try:
                if float(remaining_fruits) > 0.3:
                    self.withdraw()
                else:
                    logger.info(f'剩余水果 {remaining_fruits} <= 0.3，不满足提现条件')
            except ValueError:
                logger.error(f'无法解析水果数量: {remaining_fruits}')
        
        logger.info(f'账号 {self.wxid} 处理完成')
        return True

def send_notification(content: str, title: str = None):
    """发送微信推送通知"""
    if not DFJS_WXPUSH_TOKEN or not DFJS_WXPUSH_UID:
        return
    
    try:
        data = {
            "appToken": DFJS_WXPUSH_TOKEN,
            "content": content,
            "contentType": 1,
            "uids": DFJS_WXPUSH_UID.split(',') if ',' in DFJS_WXPUSH_UID else [DFJS_WXPUSH_UID]
        }
        
        if title:
            data["summary"] = title
        
        response = requests.post("https://wxpusher.zjiecode.com/api/send/message", json=data, timeout=10)
        result = response.json()
        
        if result.get('code') == 1000:
            logger.info('微信通知发送成功')
        else:
            logger.error(f'微信通知发送失败: {result.get("msg")}')
    except Exception as e:
        logger.error(f'微信通知发送异常: {str(e)}')

def main():
    """主函数"""
    # 检查必需的环境变量
    if not DFJS_SERVER:
        logger.error('错误: 未设置DFJS_SERVER环境变量')
        return False
    
    if not DFJS_WXID:
        logger.error('错误: 未设置DFJS_WXID环境变量')
        return False
    
    # 解析账号列表
    wxids = []
    if '&' in DFJS_WXID:
        wxids = [wxid.strip() for wxid in DFJS_WXID.split('&') if wxid.strip()]
    elif '@' in DFJS_WXID:
        wxids = [wxid.strip() for wxid in DFJS_WXID.split('@') if wxid.strip()]
    else:
        wxids = [DFJS_WXID.strip()]
    
    logger.info(f'准备处理 {len(wxids)} 个账号')
    
    # 处理账号
    success_count = 0
    failed_wxids = []
    
    for i, wxid in enumerate(wxids):
        try:
            logger.info(f'正在处理第 {i+1}/{len(wxids)} 个账号')
            client = DFJSClient(DFJS_SERVER, wxid, DFJS_APPID)
            
            if client.run():
                success_count += 1
            else:
                failed_wxids.append(wxid)
            
            # 账号间延迟
            if i < len(wxids) - 1:
                time.sleep(5)
                
        except Exception as e:
            logger.error(f'处理账号 {wxid} 时发生错误: {str(e)}')
            failed_wxids.append(wxid)
    
    # 发送汇总通知
    if DFJS_WXPUSH_TOKEN and DFJS_WXPUSH_UID:
        content = f"东方棘市任务完成\n\n" \
                 f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n" \
                 f"总账号数: {len(wxids)}\n" \
                 f"成功: {success_count}\n" \
                 f"失败: {len(failed_wxids)}"
        
        if failed_wxids:
            content += f"\n\n失败账号:\n" + "\n".join(failed_wxids[:5])
            if len(failed_wxids) > 5:
                content += f"\n... 等共{len(failed_wxids)}个"
        
        send_notification(content, f"东方棘市任务完成 - {success_count}/{len(wxids)}")
    
    logger.info(f'所有账号处理完成! 成功: {success_count}/{len(wxids)}')
    return success_count > 0

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        sys.exit(1)
