#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
东方棘市小程序自动化工具 - DFJS环境变量版本
作者: Tianxx
版本: v3.0.0 (DFJS版)
创建日期: 2025-07-30
抓token
单账号: export DFJS="c81e21112560ae8eefb1233333bac65e"
多账号: export DFJS="token1
token2
token3"
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import requests

# 东方棘市 API常量
YS_BASE_URL = "https://ys.shajixueyuan.com"
API_USER_SIGN_IN = "/api/user_sign/sign"
API_USER_INFO = "/api/user/info"
API_WITHDRAW_APPLY = "/api/user.user_withdraw/apply"

# 环境变量配置
DFJS_DATA = os.environ.get('DFJS')
DFJS_DEBUG = os.environ.get('DFJS_DEBUG', '').lower() == 'true'

# 设置日志
logging.basicConfig(
    level=logging.DEBUG if DFJS_DEBUG else logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DFJSClient:
    """东方棘市客户端 - 基于token的精简版"""
    
    def __init__(self, name: str, token: str):
        self.name = name
        self.token = token
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'version': '1.0.15',
            'content-type': 'application/json',
            'token': self.token,
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip,compress,br,deflate',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN',
            'Referer': 'https://servicewechat.com/wxebdf2c44a2a714c2/120/page-frame.html'
        }
    
    def _request_post(self, path: str, data: Dict = None) -> Optional[Dict]:
        """发送POST请求"""
        url = f"{YS_BASE_URL}{path}"
        headers = self._get_headers()
        
        try:
            logger.debug(f"POST请求: {url}")
            if data:
                logger.debug(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
            
            response = requests.post(url, json=data or {}, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()
            
            logger.debug(f"响应结果: {json.dumps(result, ensure_ascii=False)}")
            return result
        except Exception as e:
            logger.error(f"请求失败 {url}: {str(e)}")
            return None
    
    def _request_get(self, path: str) -> Optional[Dict]:
        """发送GET请求"""
        url = f"{YS_BASE_URL}{path}"
        headers = self._get_headers()
        
        try:
            logger.debug(f"GET请求: {url}")
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            result = response.json()
            
            logger.debug(f"响应结果: {json.dumps(result, ensure_ascii=False)}")
            return result
        except Exception as e:
            logger.error(f"请求失败 {url}: {str(e)}")
            return None
    
    def sign_in(self) -> bool:
        """每日签到"""
        logger.info(f'[{self.name}] 执行每日签到...')
        
        result = self._request_post(API_USER_SIGN_IN)
        
        if result and result.get('code') == 1:
            msg = result.get('msg', '签到成功')
            # 提取签到奖励信息
            rewards_info = result.get('data', {}).get('rewards_info', {})
            if rewards_info:
                fruit = rewards_info.get('fruit', 0)
                energy = rewards_info.get('energy', 0)
                logger.info(f'[{self.name}] 签到成功: {msg} (果子+{fruit}, 能量+{energy})')
            else:
                logger.info(f'[{self.name}] 签到成功: {msg}')
            return True
        else:
            error_msg = result.get('msg', '未知错误') if result else '请求失败'
            logger.warning(f'[{self.name}] 签到失败: {error_msg}')
            return False
    
    def get_user_info(self) -> Optional[Dict]:
        """获取用户信息"""
        logger.info(f'[{self.name}] 获取用户信息...')
        
        result = self._request_get(API_USER_INFO)
        
        if result and result.get('code') == 1:
            user_data = result.get('data')
            if user_data:
                remaining_fruits = user_data.get('remaining_fruits', '0')
                nickname = user_data.get('nickname', '未知用户')
                mobile = user_data.get('mobile', '未知手机号')
                logger.info(f'[{self.name}] 用户信息获取成功: {nickname} ({mobile}), 剩余水果: {remaining_fruits}')
                return user_data
        
        logger.error(f'[{self.name}] 用户信息获取失败')
        return None
    
    def withdraw(self, amount: str = "0.3") -> bool:
        """提现水果"""
        logger.info(f'[{self.name}] 尝试提现水果: {amount}')
        
        data = {
            "fruit_withdraw_amount": amount,
            "pay_gateway": "wechat"
        }
        
        result = self._request_post(API_WITHDRAW_APPLY, data)
        
        if result and result.get('code') == 1:
            msg = result.get('msg', '提现申请成功')
            logger.info(f'[{self.name}] 提现成功: {msg}')
            return True
        else:
            error_msg = result.get('msg', '未知错误') if result else '请求失败'
            if '余额不足' in error_msg or '不满足' in error_msg:
                logger.info(f'[{self.name}] 提现失败: {error_msg}')
            else:
                logger.error(f'[{self.name}] 提现失败: {error_msg}')
            return False
    
    def run(self) -> Dict[str, Any]:
        """执行完整流程"""
        logger.info(f'[{self.name}] 开始处理账号')
        
        result = {
            'name': self.name,
            'success': False,
            'sign_in': False,
            'withdraw': False,
            'remaining_fruits': '0',
            'error': None
        }
        
        try:
            # 1. 签到
            result['sign_in'] = self.sign_in()
            
            # 2. 获取用户信息
            user_info = self.get_user_info()
            if not user_info:
                result['error'] = '获取用户信息失败'
                return result
            
            # 3. 检查水果余额并尝试提现
            remaining_fruits = user_info.get('remaining_fruits', '0')
            result['remaining_fruits'] = remaining_fruits
            
            try:
                fruits_float = float(remaining_fruits)
                if fruits_float > 0.3:
                    result['withdraw'] = self.withdraw()
                else:
                    logger.info(f'[{self.name}] 剩余水果 {remaining_fruits} <= 0.3，不满足提现条件')
            except ValueError:
                logger.error(f'[{self.name}] 无法解析水果数量: {remaining_fruits}')
                result['error'] = f'无法解析水果数量: {remaining_fruits}'
            
            result['success'] = True
            logger.info(f'[{self.name}] 账号处理完成')
            
        except Exception as e:
            logger.error(f'[{self.name}] 处理过程中发生错误: {str(e)}')
            result['error'] = str(e)
        
        return result

def parse_dfjs_data(dfjs_str: str) -> List[Dict[str, str]]:
    """解析DFJS环境变量数据"""
    try:
        accounts = []

        # 按行分割token
        tokens = [token.strip() for token in dfjs_str.strip().split('\n') if token.strip()]

        for i, token in enumerate(tokens):
            # 验证token格式（32位十六进制字符串）
            if len(token) == 32 and all(c in '0123456789abcdefABCDEF' for c in token):
                accounts.append({
                    "name": f"账号{i+1}" if len(tokens) > 1 else "默认账号",
                    "token": token
                })
            else:
                logger.warning(f'跳过无效token: {token[:10]}... (长度: {len(token)})')

        return accounts

    except Exception as e:
        logger.error(f'DFJS环境变量解析失败: {str(e)}')
        return []



def main():
    """主函数"""
    # 检查必需的环境变量
    if not DFJS_DATA:
        logger.error('错误: 未设置DFJS环境变量')
        print('请设置DFJS环境变量，格式示例:')
        print('单账号: export DFJS="c81e21112560ae8eefbc0e732bbac65e"')
        print('多账号: export DFJS="token1\\ntoken2\\ntoken3"')
        return False
    
    # 解析账号数据
    accounts = parse_dfjs_data(DFJS_DATA)
    if not accounts:
        logger.error('DFJS环境变量解析失败或无有效账号')
        return False
    
    logger.info(f'准备处理 {len(accounts)} 个账号')
    
    # 处理账号
    results = []
    
    for i, account in enumerate(accounts):
        try:
            logger.info(f'正在处理第 {i+1}/{len(accounts)} 个账号: {account["name"]}')
            client = DFJSClient(account['name'], account['token'])
            
            result = client.run()
            results.append(result)
            
            # 账号间延迟
            if i < len(accounts) - 1:
                time.sleep(3)
                
        except Exception as e:
            logger.error(f'处理账号 {account["name"]} 时发生错误: {str(e)}')
            results.append({
                'name': account['name'],
                'success': False,
                'error': str(e)
            })
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    sign_in_count = sum(1 for r in results if r.get('sign_in'))
    withdraw_count = sum(1 for r in results if r.get('withdraw'))
    failed_accounts = [r['name'] for r in results if not r['success']]
    
    # 输出汇总信息
    logger.info(f"=== 任务执行汇总 ===")
    logger.info(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"总账号数: {len(accounts)}")
    logger.info(f"处理成功: {success_count}")
    logger.info(f"签到成功: {sign_in_count}")
    logger.info(f"提现成功: {withdraw_count}")

    if failed_accounts:
        logger.warning(f"失败账号({len(failed_accounts)}个): {', '.join(failed_accounts[:5])}")
        if len(failed_accounts) > 5:
            logger.warning(f"... 等共{len(failed_accounts)}个账号失败")

    # 显示部分账号的水果余额
    for r in results[:3]:  # 只显示前3个账号
        if r['success'] and r.get('remaining_fruits'):
            logger.info(f"账号 {r['name']} 剩余水果: {r['remaining_fruits']}")

    logger.info("=" * 20)
    
    logger.info(f'所有账号处理完成! 成功: {success_count}/{len(accounts)}, 签到: {sign_in_count}, 提现: {withdraw_count}')
    return success_count > 0

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        sys.exit(1)
