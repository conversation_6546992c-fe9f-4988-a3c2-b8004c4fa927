#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
东方棘市 DFJS 版本测试示例
"""

import os
import sys

# 测试用的环境变量设置示例
def test_single_account():
    """测试单账号配置"""
    print("=== 单账号测试 ===")
    
    # 设置环境变量
    os.environ['DFJS'] = '{"token": "your_token_here"}'
    os.environ['DFJS_DEBUG'] = 'true'
    
    print("环境变量 DFJS:", os.environ.get('DFJS'))
    print("环境变量 DFJS_DEBUG:", os.environ.get('DFJS_DEBUG'))
    
    # 这里可以导入并运行脚本进行测试
    # import 东方棘市_DFJS
    # 东方棘市_DFJS.main()

def test_multiple_accounts():
    """测试多账号配置"""
    print("\n=== 多账号测试 ===")
    
    # 多账号配置示例
    dfjs_config = {
        "账号1": {"token": "token1_here"},
        "账号2": {"token": "token2_here"},
        "账号3": {"token": "token3_here"}
    }
    
    import json
    os.environ['DFJS'] = json.dumps(dfjs_config, ensure_ascii=False)
    os.environ['DFJS_DEBUG'] = 'true'
    
    print("环境变量 DFJS:", os.environ.get('DFJS'))

def test_array_format():
    """测试数组格式配置"""
    print("\n=== 数组格式测试 ===")
    
    dfjs_config = [
        {"name": "账号A", "token": "tokenA_here"},
        {"name": "账号B", "token": "tokenB_here"}
    ]
    
    import json
    os.environ['DFJS'] = json.dumps(dfjs_config, ensure_ascii=False)
    
    print("环境变量 DFJS:", os.environ.get('DFJS'))

def test_with_push():
    """测试带推送配置"""
    print("\n=== 推送配置测试 ===")
    
    os.environ['DFJS'] = '{"测试账号": "test_token"}'
    os.environ['DFJS_WXPUSH_TOKEN'] = 'your_wxpusher_token'
    os.environ['DFJS_WXPUSH_UID'] = 'your_uid1,your_uid2'
    
    print("推送Token:", os.environ.get('DFJS_WXPUSH_TOKEN'))
    print("推送UID:", os.environ.get('DFJS_WXPUSH_UID'))

def show_usage_examples():
    """显示使用示例"""
    print("\n=== 使用示例 ===")
    
    print("1. 青龙面板环境变量配置:")
    print('   DFJS={"账号1": "token1", "账号2": "token2"}')
    
    print("\n2. 命令行使用:")
    print('   export DFJS=\'{"账号1": "your_token"}\'')
    print('   python3 东方棘市_DFJS.py')
    
    print("\n3. Docker 使用:")
    print('   docker run -e DFJS=\'{"账号1": "token1"}\' python:3.9 python script.py')
    
    print("\n4. 获取真实 token 的方法:")
    print("   - 运行原版脚本登录后查看 dfjs_tokens.json 文件")
    print("   - 使用抓包工具获取 API 请求中的 token 请求头")
    print("   - Token 示例: 29454631e2b728d239686c4592c5fe69")

if __name__ == "__main__":
    print("东方棘市 DFJS 版本配置测试")
    print("=" * 50)
    
    test_single_account()
    test_multiple_accounts()
    test_array_format()
    test_with_push()
    show_usage_examples()
    
    print("\n" + "=" * 50)
    print("测试完成！请根据上述示例配置您的环境变量。")
    print("注意：需要将示例中的 token 替换为真实的 token 值。")
