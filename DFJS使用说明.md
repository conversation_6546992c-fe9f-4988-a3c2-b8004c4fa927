# 东方棘市 DFJS 环境变量版本使用说明

## 概述

这是东方棘市小程序自动化工具的精简版本，完全基于环境变量 `DFJS` 中的 token 进行操作，无需获取微信 code 或重新登录。

## 主要功能

- ✅ **每日签到**: 自动执行每日签到任务
- ✅ **用户信息查询**: 获取用户信息和水果余额
- ✅ **自动提现**: 当水果余额 > 0.3 时自动提现 0.3
- ✅ **多账号支持**: 支持批量处理多个账号
- ✅ **微信推送**: 可选的任务完成通知

## 环境变量配置

### 必需环境变量

#### `DFJS`
包含 token 的字符串，支持单账号和多账号：

**单账号格式:**
```bash
export DFJS="c81e21112560ae8eefbc0e732bbac65e"
```

**多账号格式 (每行一个token):**
```bash
export DFJS="c81e21112560ae8eefbc0e732bbac65e
29454631e2b728d239686c4592c5fe69
a1b2c3d4e5f6789012345678901234ab"
```

**或者在青龙面板中设置多行:**
```
c81e21112560ae8eefbc0e732bbac65e
29454631e2b728d239686c4592c5fe69
a1b2c3d4e5f6789012345678901234ab
```

### 可选环境变量

- `DFJS_DEBUG`: 调试模式，设置为 `true` 启用详细日志

## 如何获取 Token

Token 需要从原版脚本的登录过程中获取，或者通过抓包获得：

1. **从原版脚本获取**: 运行原版脚本成功登录后，查看 `dfjs_tokens.json` 文件中的 `app_token` 字段
2. **抓包获取**: 使用抓包工具获取登录后的 API 请求中的 `token` 请求头

## 使用示例

### 青龙面板配置

1. 在青龙面板中添加环境变量：
   ```
   名称: DFJS
   值: c81e21112560ae8eefbc0e732bbac65e
   29454631e2b728d239686c4592c5fe69
   ```

2. 可选添加调试配置：
   ```
   名称: DFJS_DEBUG
   值: true
   ```

3. 添加定时任务：
   ```
   名称: 东方棘市签到
   命令: python3 /path/to/东方棘市_DFJS.py
   定时规则: 0 8 * * *  # 每天8点执行
   ```

### 命令行使用

```bash
# 设置环境变量 - 单账号
export DFJS="c81e21112560ae8eefbc0e732bbac65e"

# 设置环境变量 - 多账号
export DFJS="c81e21112560ae8eefbc0e732bbac65e
29454631e2b728d239686c4592c5fe69"

# 可选：启用调试模式
export DFJS_DEBUG=true

# 运行脚本
python3 东方棘市_DFJS.py
```

### Docker 使用

```bash
docker run -e DFJS="c81e21112560ae8eefbc0e732bbac65e" -v /path/to/script:/app python:3.9 python /app/东方棘市_DFJS.py
```

## 输出示例

### 正常运行输出
```
2025-07-30 08:00:01 - INFO - 准备处理 2 个账号
2025-07-30 08:00:01 - INFO - 正在处理第 1/2 个账号: 账号1
2025-07-30 08:00:01 - INFO - [账号1] 开始处理账号
2025-07-30 08:00:02 - INFO - [账号1] 执行每日签到...
2025-07-30 08:00:03 - INFO - [账号1] 签到成功: 签到成功 (果子+0.16, 能量+0.00)
2025-07-30 08:00:03 - INFO - [账号1] 获取用户信息...
2025-07-30 08:00:04 - INFO - [账号1] 用户信息获取成功: 用户94298579 (18808333396), 剩余水果: 0.46
2025-07-30 08:00:04 - INFO - [账号1] 尝试提现水果: 0.3
2025-07-30 08:00:05 - INFO - [账号1] 提现成功: 果子提现申请成功
2025-07-30 08:00:05 - INFO - [账号1] 账号处理完成
2025-07-30 08:00:08 - INFO - 正在处理第 2/2 个账号: 账号2
...
2025-07-30 08:00:15 - INFO - 所有账号处理完成! 成功: 2/2, 签到: 2, 提现: 1
```

### 控制台汇总输出示例
```
2025-07-30 08:00:15 - INFO - === 任务执行汇总 ===
2025-07-30 08:00:15 - INFO - 执行时间: 2025-07-30 08:00:15
2025-07-30 08:00:15 - INFO - 总账号数: 2
2025-07-30 08:00:15 - INFO - 处理成功: 2
2025-07-30 08:00:15 - INFO - 签到成功: 2
2025-07-30 08:00:15 - INFO - 提现成功: 1
2025-07-30 08:00:15 - INFO - 账号 账号1 剩余水果: 0.16
2025-07-30 08:00:15 - INFO - 账号 账号2 剩余水果: 0.28
2025-07-30 08:00:15 - INFO - ====================
```

## 注意事项

1. **Token 有效期**: Token 可能会过期，需要定期更新
2. **请求频率**: 脚本已内置账号间 3 秒延迟，避免请求过于频繁
3. **错误处理**: 脚本会自动处理常见错误，如余额不足等
4. **日志记录**: 建议启用调试模式进行首次测试

## 故障排除

### 常见问题

1. **Token 无效**
   - 检查 token 是否正确
   - 尝试重新获取 token

2. **Token 格式错误**
   - 确保 token 是32位十六进制字符串
   - 检查是否有多余的空格或换行符

3. **网络请求失败**
   - 检查网络连接
   - 确认服务器地址正确



### 调试模式

设置 `DFJS_DEBUG=true` 可以查看详细的请求和响应信息，有助于排查问题。

## 版本历史

- v3.0.0: 完全重构，基于 DFJS 环境变量，移除微信登录逻辑
- v2.0.0: 精简版本
- v1.2.0: 原始完整版本

## 联系方式

如有问题或建议，请联系作者 Tianxx。
