#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
东方棘市小程序自动化工具 - 精简环境变量版本
作者: Tianxx
版本: v2.0.0 (精简版)
创建日期: 2025-07-30
"""

# 标准库导入
import os
import sys
import json
import time
import logging
from datetime import datetime
from typing import Dict, Optional, Any

# 第三方库导入
import requests

# 东方棘市 相关API常量
YS_BASE_URL = "https://ys.shajixueyuan.com"
API_MNP_LOGIN = "/api/login/mnpLogin"
API_USER_SIGN_IN = "/api/user_sign/sign"
API_USER_INFO = "/api/user/info"
API_WITHDRAW_APPLY = "/api/user.user_withdraw/apply"

# 环境变量配置 - 使用DFJS前缀
DFJS_SERVER = os.environ.get('DFJS_SERVER')  # 微信中间服务器地址
DFJS_WXID = os.environ.get('DFJS_WXID')      # 微信ID(支持多账号，用&分隔)
DFJS_APPID = os.environ.get('DFJS_APPID', 'wxebdf2c44a2a714c2')  # 小程序AppID
DFJS_DEBUG = os.environ.get('DFJS_DEBUG', '').lower() == 'true'   # 调试模式
DFJS_WXPUSH_TOKEN = os.environ.get('DFJS_WXPUSH_TOKEN')  # 微信推送Token
DFJS_WXPUSH_UID = os.environ.get('DFJS_WXPUSH_UID')      # 微信推送UID

def get_script_info() -> Dict[str, str]:
    """从脚本名获取信息，用于生成token文件名和环境变量前缀"""
    # 获取脚本的完整路径和文件名
    script_path = os.path.abspath(sys.argv[0])
    script_name = os.path.basename(script_path)
    script_name_no_ext = os.path.splitext(script_name)[0]
    
    # 处理脚本名称，保留ASCII字符
    safe_name = re.sub(r'[^a-zA-Z0-9]', '', script_name_no_ext)
    
    # 生成环境变量前缀（大写）
    env_prefix = safe_name.upper() if safe_name else ''
    
    # 如果脚本名全是中文或特殊字符（没有英文字母和数字）
    if not env_prefix:
        # 简化处理：使用中文的Unicode编码区间特征
        # 提取最多前3个字符各自的首字母特征值
        prefix_chars = []
        for ch in script_name_no_ext[:3]:
            if '\u4e00' <= ch <= '\u9fff':  # 中文字符
                # 简单算法：将Unicode码点映射到A-Z
                # 这里将中文Unicode码点范围(0x4e00-0x9fff)映射到26个字母
                index = (ord(ch) - 0x4e00) % 26
                prefix_chars.append(chr(65 + index))  # A-Z
            else:
                # 非中文字符，尝试直接使用
                try:
                    prefix_chars.append(ch.encode('ascii').decode('ascii').upper())
                except:
                    # 无法转换为ASCII的字符，使用X代替
                    prefix_chars.append('X')
        
        env_prefix = ''.join(prefix_chars) if prefix_chars else 'PC'
    
    # 如果生成的前缀少于2个字符，补充到至少2个字符
    if len(env_prefix) < 2:
        env_prefix += 'PC'[0:(2-len(env_prefix))]
    
    # 使用与环境变量前缀相同的逻辑生成token文件名，但为小写
    token_name = env_prefix.lower()
    
    return {
        "script_path": script_path,
        "script_name": script_name,
        "env_prefix": env_prefix,
        "token_name": token_name
    }

# 定义HTTP客户端接口，便于依赖注入和单元测试
class HttpClient(ABC):
    """HTTP客户端接口，用于依赖注入
    
    定义统一的HTTP请求接口，便于在单元测试中模拟HTTP请求
    """
    
    @abstractmethod
    def post(self, url: str, json: Dict[str, Any] = None, headers: Dict[str, str] = None, 
            timeout: int = 30) -> Dict[str, Any]:
        """发送POST请求
        
        Args:
            url: 请求URL
            json: 请求体数据
            headers: 请求头
            timeout: 超时时间（秒）
            
        Returns:
            Dict: 响应数据
            
        Raises:
            Exception: 请求失败时抛出异常
        """
        pass

    @abstractmethod
    def get(self, url: str, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None,
            timeout: int = 30) -> Dict[str, Any]:
        """发送GET请求
        
        Args:
            url: 请求URL
            params: URL参数
            headers: 请求头
            timeout: 超时时间（秒）
            
        Returns:
            Dict: 响应数据
            
        Raises:
            Exception: 请求失败时抛出异常
        """
        pass


# 基于requests实现HTTP客户端
class RequestsHttpClient(HttpClient):
    """基于requests库实现的HTTP客户端"""
    
    def post(self, url: str, json: Dict[str, Any] = None, headers: Dict[str, str] = None, 
            timeout: int = 30) -> Dict[str, Any]:
        """使用requests库发送POST请求"""
        response = requests.post(url, json=json, headers=headers, timeout=timeout)
        response.raise_for_status()  # 抛出HTTP错误
        return response.json()

    def get(self, url: str, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None,
            timeout: int = 30) -> Dict[str, Any]:
        """使用requests库发送GET请求"""
        response = requests.get(url, params=params, headers=headers, timeout=timeout)
        response.raise_for_status()  # 抛出HTTP错误
        return response.json()


class WxPusher:
    """微信推送工具类，用于发送微信通知"""
    
    def __init__(self, app_token=None, uids=None, logger=None):
        """初始化微信推送工具
        
        Args:
            app_token: WxPusher的AppToken
            uids: 接收通知的用户ID列表
            logger: 日志记录器
        """
        self.app_token = app_token or os.environ.get('WXPUSH_TOKEN') or DEFAULT_WXPUSH_TOKEN
        
        # 处理UID - 可以是单个UID或多个UID的列表
        if uids:
            self.uids = uids if isinstance(uids, list) else [uids]
        else:
            env_uid = os.environ.get('WXPUSH_UID') or DEFAULT_WXPUSH_UID
            self.uids = env_uid.split(',') if ',' in env_uid else [env_uid]
        
        self.logger = logger or logging.getLogger("wxpusher")
    
    def send(self, content, title=None, url=None) -> bool:
        """发送微信通知
        
        Args:
            content: 通知内容
            title: 通知标题，默认为None
            url: 点击通知后跳转的URL，默认为None
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        try:
            # 构建请求数据
            data = {
                "appToken": self.app_token,
                "content": content,
                "contentType": 1,  # 1表示文本，2表示HTML
                "uids": self.uids
            }
            
            if title:
                data["summary"] = title  # 摘要，显示在通知中心
            
            if url:
                data["url"] = url  # 点击跳转的URL
            
            # 发送请求
            response = requests.post(WXPUSHER_API, json=data, timeout=10)
            result = response.json()
            
            if result.get('code') == 1000:
                if self.logger:
                    self.logger.info(f"微信通知发送成功: {title or '无标题'}")
                return True
            else:
                if self.logger:
                    self.logger.error(f"微信通知发送失败: {result.get('msg')}")
                return False
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"微信通知发送异常: {str(e)}")
            return False

class PhonecodeClient:
    """小程序登录客户端类
    
    该类提供与小程序登录相关的功能，包括获取登录code、获取token和获取手机号等。
    可以被其他脚本导入使用，也可以作为独立脚本运行。
    
    基本用法:
        client = PhonecodeClient(server, wxid, appid)
        if client.login_process():
            # 登录成功，可以进行其他操作
            mobile_data = client.get_mobile_number()
    """
    
    # 默认的小程序APPID，根据需要修改
    DEFAULT_APPID = "wxebdf2c44a2a714c2" # 更新为东方棘市AppID
    
    def __init__(self, server: str, wxid: str, appid: Optional[str] = None, 
                 log_level: int = logging.INFO, token_file: Optional[str] = None,
                 http_client: Optional[HttpClient] = None, enable_push: bool = False,
                 wxpush_token: Optional[str] = None, wxpush_uid: Optional[str] = None):
        """初始化客户端
        
        Args:
            server: 微信中间服务器地址，格式为IP:PORT或完整URL。这是处理微信登录的服务器，
                  不是小程序应用自身的服务器地址。
            wxid: 微信ID
            appid: 小程序AppID，默认使用类中定义的DEFAULT_APPID
            log_level: 日志级别，默认为INFO
            token_file: token保存文件路径，默认为脚本同目录下的{脚本名}_tokens.json
            http_client: HTTP客户端实现，用于依赖注入
            enable_push: 是否启用微信通知推送，默认为关闭
            wxpush_token: WxPusher的AppToken
            wxpush_uid: 接收通知的用户ID
        """
        # 基本配置
        self.server = server  # 微信中间服务器地址
        self.wxid = wxid
        self.appid = appid if appid else self.DEFAULT_APPID  # 使用传入的APPID或默认值
        
        # 初始化应用Token
        self.app_token: Optional[str] = None
        
        # 设置日志
        self.setup_logger(log_level)
        
        # 设置HTTP客户端 - 依赖注入模式
        self.http_client = http_client or RequestsHttpClient()
        
        # Token文件路径
        if token_file:
            self.token_file = token_file
        else:
            # 获取脚本路径用于确定保存目录
            script_path = get_script_info()["script_path"]
            # 始终保存在脚本同目录下，并使用固定的文件名
            self.token_file = os.path.join(os.path.dirname(script_path), 'dfjs_tokens.json')
            
            self.log_msg(f"Token将保存至: {self.token_file}", logging.DEBUG)
        
        # 微信推送设置
        self.enable_push = enable_push
        if self.enable_push:
            self.pusher = WxPusher(wxpush_token, wxpush_uid, self.logger)
            
    def setup_logger(self, log_level: int) -> None:
        """设置日志系统"""
        self.logger = logging.getLogger("phonecode")
        self.logger.setLevel(log_level)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)
            
            # 日志格式
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            self.logger.addHandler(console_handler)
    
    def log_msg(self, msg: str, level: int = logging.INFO) -> None:
        """记录日志消息"""
        if level == logging.DEBUG:
            self.logger.debug(msg)
        elif level == logging.INFO:
            self.logger.info(msg)
        elif level == logging.WARNING:
            self.logger.warning(msg)
        elif level == logging.ERROR:
            self.logger.error(msg)
    
    def _get_ys_common_headers(self, app_token: Optional[str] = None) -> Dict[str, str]:
        """获取调用东方棘市API的通用请求头"""
        headers = {
            'accept': 'application/json',
            'content-type': 'application/json',
            'Referer': f'https://servicewechat.com/{self.appid}/163/page-frame.html', # 假设版本号163是固定的，或者可以动态获取
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/9129'
        }
        if app_token:
            # 修改token的头名称，根据API错误信息，服务器期望的可能是'token'而不是'x-token'
            headers['token'] = app_token
            # 同时保留x-token以防需要
            headers['x-token'] = app_token
        return headers

    def common_post(self, url: str, body: Dict[str, Any], headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """公共POST请求方法 (用于微信中间服务器)"""
        # 构建请求头
        if headers is None:
            headers = {
                'accept': 'application/json',
                'content-type': 'application/json'
            }
        
        # 处理URL
        api_url = url
        if url.startswith('/'):
            server_address = self.server
            if not server_address.startswith(('http://', 'https://')):
                server_address = f"http://{server_address}"
            api_url = f"{server_address}/api{url}"
        
        try:
            self.logger.debug(f"请求URL: {api_url}")
            self.logger.debug(f"请求Body: {body}")
            
            # 使用注入的HTTP客户端发送请求
            result = self.http_client.post(api_url, json=body, headers=headers, timeout=30)
            
            self.logger.debug(f"响应结果: {result}")
            return result
        except Exception as e:
            self.logger.error(f"请求失败: {str(e)}")
            return None
    
    def common_ys_post(self, path: str, body: Dict[str, Any], headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """公共POST请求方法 (用于东方棘市服务器)"""
        api_url = f"{YS_BASE_URL}{path}"
        
        # 为所有请求都添加app_token参数（如果存在），确保token也在URL参数或请求体中传递
        if self.app_token and isinstance(body, dict):
            # 在请求体中添加token（某些API可能需要）
            body_with_token = body.copy()  # 创建副本以免修改原始对象
            body_with_token['token'] = self.app_token
        else:
            body_with_token = body
        
        try:
            self.logger.debug(f"请求东方棘市URL (POST): {api_url}")
            self.logger.debug(f"请求Body: {json.dumps(body_with_token, ensure_ascii=False)}") # 使用json.dumps打印中文
            
            # 使用注入的HTTP客户端发送请求
            result = self.http_client.post(api_url, json=body_with_token, headers=headers, timeout=30)
            
            self.logger.debug(f"响应结果: {json.dumps(result, ensure_ascii=False)}")
            return result
        except Exception as e:
            self.logger.error(f"请求东方棘市API (POST {path}) 失败: {str(e)}")
            return None

    def common_ys_get(self, path: str, params: Optional[Dict[str, Any]] = None, headers: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """公共GET请求方法 (用于东方棘市服务器)"""
        api_url = f"{YS_BASE_URL}{path}"
        
        # 为GET请求添加token参数
        params_with_token = {} if params is None else params.copy()
        if self.app_token:
            params_with_token['token'] = self.app_token
        
        try:
            self.logger.debug(f"请求东方棘市URL (GET): {api_url}")
            self.logger.debug(f"请求Params: {params_with_token}")
            
            # 使用注入的HTTP客户端发送请求
            result = self.http_client.get(api_url, params=params_with_token, headers=headers, timeout=30)
            
            self.logger.debug(f"响应结果: {json.dumps(result, ensure_ascii=False)}")
            return result
        except Exception as e:
            self.logger.error(f"请求东方棘市API (GET {path}) 失败: {str(e)}")
            return None

    def get_login_code(self) -> Optional[str]:
        """获取小程序登录code (微信JSLogin)
        
        Returns:
            str: 登录code，获取失败则返回None
        """
        self.log_msg('开始获取小程序登录code...', logging.DEBUG)
        
        try:
            result = self.common_post('/Wxapp/JSLogin', {
                "Appid": self.appid,
                "Wxid": self.wxid
            })
            
            if not result or not result.get('Success'):
                self.log_msg(f"获取小程序授权code失败", logging.ERROR)
                return None
            
            code = result.get('Data', {}).get('code')
            self.log_msg(f"成功获取小程序授权code", logging.DEBUG)
            
            # 保存code作为token数据
            if code:
                token_data = {
                    "code": code,
                    "expireTime": int(time.time()) + 300  # 假设5分钟有效期
                }
                self.save_token(token_data) # 保存的是微信的js_code
                self.log_msg('微信JSLogin Code保存成功!', logging.DEBUG)
            
            return code
        except Exception as e:
            self.log_msg(f"获取code过程出错: {str(e)}", logging.ERROR)
            return None
    
    def code_to_token(self, code: str) -> Optional[Dict[str, Any]]:
        """使用code换取token
        
        注意：该方法保留用于兼容性，实际上已经在get_login_code方法中处理了token保存
        
        Args:
            code: 登录code
            
        Returns:
            dict: token数据，获取失败则返回None
        """
        # 直接使用code作为token返回
        token_data = {
            "code": code,
            "expireTime": int(time.time()) + 300  # 假设5分钟有效期
        }
        return token_data
    
    def save_token(self, token_data: Dict[str, Any]) -> bool:
        """保存token到文件
        
        Args:
            token_data: token数据（字典）
            
        Returns:
            bool: 保存成功返回True，失败返回False
        """
        try:
            # 加载现有tokens
            tokens = {}
            if os.path.exists(self.token_file):
                with open(self.token_file, 'r', encoding='utf-8') as f:
                    try:
                        tokens = json.load(f)
                    except json.JSONDecodeError:
                        self.log_msg(f"Token文件 {self.token_file} 内容为空或格式错误，将创建新的。", logging.WARNING)
                        tokens = {}
            
            # 获取当前wxid的token数据，如果不存在则创建一个新的字典
            current_wxid_tokens = tokens.get(self.wxid, {})
            
            # 更新appid和时间戳总是进行
            current_wxid_tokens["appid"] = self.appid
            current_wxid_tokens["timestamp"] = int(time.time())
            current_wxid_tokens["update_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 合并新的token数据
            current_wxid_tokens.update(token_data)
            
            # 将更新后的数据存回主tokens字典
            tokens[self.wxid] = current_wxid_tokens
            
            # 保存到文件
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(tokens, f, ensure_ascii=False, indent=2)
            
            self.log_msg(f"Token数据已更新并保存: {list(token_data.keys())}", logging.DEBUG)
            return True
        except Exception as e:
            self.log_msg(f"保存token失败: {str(e)}", logging.ERROR)
            return False
    
    def get_token(self) -> Optional[Dict[str, Any]]:
        """从文件中获取已保存的token
        
        Returns:
            dict: token数据，不存在则返回None
        """
        try:
            if not os.path.exists(self.token_file):
                return None
                
            with open(self.token_file, 'r', encoding='utf-8') as f:
                tokens = json.load(f)
                
            if self.wxid in tokens:
                return tokens[self.wxid]
            return None
        except Exception as e:
            self.log_msg(f"获取已保存token失败: {str(e)}", logging.ERROR)
            return None
    
    def _mnp_login(self, wx_login_code: str, phone_auth_code: str) -> Optional[str]:
        """使用微信登录code和手机授权code换取东方棘市应用token"""
        self.log_msg('开始使用微信code和手机授权code登录东方棘市...', logging.DEBUG)
        body = {
            "code": wx_login_code,         # 这是来自 JSLogin 的 code
            "phone_code": phone_auth_code, # 这是来自 /Wxapp/GetAllMobile 的 code
            "appid": self.appid
        }
        headers = self._get_ys_common_headers()
        
        result = self.common_ys_post(API_MNP_LOGIN, body=body, headers=headers)
        
        # 根据用户提供的日志和接口文件，此登录接口成功时 code 为 1
        if result and result.get('code') == 1:
            app_token = result.get('data', {}).get('token')
            if app_token:
                self.log_msg('成功获取东方棘市AppToken', logging.DEBUG)
                return str(app_token) # 确保是字符串
            else:
                self.log_msg('东方棘市AppToken获取失败: "token"字段缺失或为空 (但业务code为1)', logging.ERROR)
                self.log_msg(f"详细响应: {json.dumps(result, ensure_ascii=False)}", logging.DEBUG)
                return None
        else:
            error_msg = result.get('msg', '未知错误') if result else '请求失败或响应为空'
            # 如果 code 不是1，也记录一下实际的 code 值
            actual_code = result.get('code', '未知code') if result else '响应为空'
            self.log_msg(f'东方棘市登录失败: {error_msg} (业务code: {actual_code})', logging.ERROR)
            self.log_msg(f"详细响应: {json.dumps(result, ensure_ascii=False)}", logging.DEBUG)
            return None

    def _user_sign_in(self, app_token: str) -> bool:
        """执行东方棘市每日签到"""
        self.log_msg('开始执行东方棘市每日签到...', logging.DEBUG)
        headers = self._get_ys_common_headers(app_token=app_token)
        
        # 签到可能需要token作为请求体参数，而不仅是头
        body = {"token": app_token}
        
        result = self.common_ys_post(API_USER_SIGN_IN, body=body, headers=headers)
        
        if result and result.get('code') == 0: # 假设成功的业务code是0
            self.log_msg(f"东方棘市签到成功: {result.get('msg', '操作成功')}", logging.INFO)
            return True
        else:
            error_msg = result.get('msg', '未知错误') if result else '请求失败或响应为空'
            self.log_msg(f'东方棘市签到失败: {error_msg}', logging.ERROR)
            self.log_msg(f"详细响应: {json.dumps(result, ensure_ascii=False)}", logging.DEBUG)
            return False

    def _get_user_info(self, app_token: str) -> Optional[Dict[str, Any]]:
        """获取东方棘市用户信息"""
        self.log_msg('开始获取东方棘市用户信息...', logging.DEBUG)
        headers = self._get_ys_common_headers(app_token=app_token)
        
        # 为GET请求准备参数，确保包含token
        params = {"token": app_token}
        
        result = self.common_ys_get(API_USER_INFO, params=params, headers=headers)
        
        if result and result.get('code') == 1:  # API实际成功返回码是1，不是0
            user_data = result.get('data')
            if user_data:
                self.log_msg('成功获取东方棘市用户信息', logging.DEBUG)
                # 特别记录剩余水果数量，这是提现判断依据
                if 'remaining_fruits' in user_data:
                    self.log_msg(f"当前剩余水果: {user_data['remaining_fruits']}", logging.INFO)
                self.log_msg(f"用户信息: {json.dumps(user_data, ensure_ascii=False)}", logging.DEBUG)
                return user_data
            else:
                self.log_msg('获取用户信息失败: "data"字段缺失或为空', logging.ERROR)
                self.log_msg(f"详细响应: {json.dumps(result, ensure_ascii=False)}", logging.DEBUG)
                return None
        else:
            error_msg = result.get('msg', '未知错误') if result else '请求失败或响应为空'
            self.log_msg(f'获取用户信息失败: {error_msg}', logging.ERROR)
            self.log_msg(f"详细响应: {json.dumps(result, ensure_ascii=False)}", logging.DEBUG)
            return None

    def _withdraw_fruits(self, app_token: str, amount: str) -> bool:
        """执行东方棘市水果提现"""
        self.log_msg(f'尝试从东方棘市提现水果: {amount} ...', logging.INFO)
        headers = self._get_ys_common_headers(app_token=app_token)
        body = {
            "fruit_withdraw_amount": amount,  # 根据接口文档，参数名称是 fruit_withdraw_amount
            "pay_gateway": "wechat"  # 添加支付网关参数，设置为微信
        }
        
        result = self.common_ys_post(API_WITHDRAW_APPLY, body=body, headers=headers)
        
        if result and result.get('code') == 1:  # 根据接口文档，成功返回code为1，不是0
            self.log_msg(f"东方棘市提现请求成功: {result.get('msg', '操作成功')}", logging.INFO)
            return True
        else:
            error_msg = result.get('msg', '未知错误') if result else '请求失败或响应为空'
            # 特别处理余额不足等情况，如果API有特定code
            if result and '余额不足' in result.get('msg',''):  # 根据错误消息判断
                 self.log_msg(f'东方棘市提现失败: {error_msg}', logging.INFO)  # INFO级别，不算严重错误
            else:
                self.log_msg(f'东方棘市提现失败: {error_msg}', logging.ERROR)
            self.log_msg(f"详细响应: {json.dumps(result, ensure_ascii=False)}", logging.DEBUG)
            return False
            
    def get_mobile_number(self) -> Optional[Dict[str, Any]]:
        """获取小程序手机号
        
        Returns:
            dict: 手机号信息，获取失败则返回None
        """
        self.log_msg('正在获取小程序手机号...', logging.DEBUG)
        
        try:
            result = self.common_post('/Wxapp/GetAllMobile', {
                "Appid": self.appid,
                "Wxid": self.wxid
            })
            
            if not result or not result.get('Success'):
                self.log_msg(f"获取小程序手机号失败: {result.get('Message', '未知错误')}", logging.ERROR)
                return None
            
            mobile_data = result.get('Data', {})
            self.log_msg(f"成功获取小程序手机号", logging.DEBUG)
            return mobile_data
        except Exception as e:
            self.log_msg(f"获取手机号过程出错: {str(e)}", logging.ERROR)
            return None
    
    def login_process(self) -> bool:
        """完整登录流程
        
        执行完整的登录过程，包括获取code, 登录, 签到, 查询用户信息, 提现等
        
        Returns:
            bool: 登录成功返回True，失败返回False
        """
        # 验证必要参数
        if not self.wxid:
            self.log_msg('错误：未配置WXID', logging.ERROR)
            return False
        
        # 1. 获取微信JSLogin Code (wx_login_code)
        wx_login_code = self.get_login_code()
        if not wx_login_code:
            self.log_msg('未能获取微信JSLogin Code，登录流程终止。', logging.ERROR)
            return False
        
        # 2. 获取手机授权Code (phone_auth_code) - 通过 /Wxapp/GetAllMobile 接口
        # 注意：get_mobile_number() 返回的是包含手机号和相关code的字典
        self.log_msg('尝试获取手机授权信息 (包含phone_auth_code)...', logging.DEBUG)
        mobile_info_data = self.get_mobile_number() # 调用原有的获取手机号方法
        if not mobile_info_data:
            self.log_msg('未能获取手机授权信息 (mobile_info_data为空)，登录流程终止。', logging.ERROR)
            return False
        
        phone_auth_code = None
        all_mobile_list = mobile_info_data.get('ALLMobile')
        if all_mobile_list and isinstance(all_mobile_list, list) and len(all_mobile_list) > 0:
            first_mobile_entry = all_mobile_list[0]
            if isinstance(first_mobile_entry, dict):
                phone_auth_code = first_mobile_entry.get('code')

        if not phone_auth_code:
            self.log_msg('手机授权信息中未找到有效的 "code" (phone_auth_code)。登录流程终止。', logging.ERROR)
            self.log_msg(f"获取到的手机信息详情 (mobile_info_data): {json.dumps(mobile_info_data, ensure_ascii=False)}", logging.DEBUG)
            return False
        self.log_msg(f'成功获取手机授权code: {phone_auth_code[:10]}... (为安全截断显示)', logging.DEBUG)

        # 3. 使用 wx_login_code 和 phone_auth_code 登录东方棘市，获取 app_token
        self.app_token = self._mnp_login(wx_login_code, phone_auth_code)
        if not self.app_token:
            self.log_msg('未能获取东方棘市AppToken，登录流程终止。', logging.ERROR)
            return False
        
        # 保存 app_token (wx_login_code 已在 get_login_code 中保存)
        self.save_token({"app_token": self.app_token})
        self.log_msg('东方棘市AppToken已保存。', logging.DEBUG)
        
        # 4. 执行签到
        sign_in_success = self._user_sign_in(self.app_token)
        # The following logs are redundant as _user_sign_in already logs the outcome.
        # if sign_in_success:
        #     self.log_msg('每日签到成功。', logging.INFO)
        # else:
        #     self.log_msg('每日签到失败。', logging.WARNING)

        # 5. 查询个人信息
        user_info = self._get_user_info(self.app_token)
        if not user_info:
            self.log_msg('未能获取用户个人信息。', logging.WARNING)
            # 即使获取信息失败，之前的登录和签到可能已成功
            return True # 认为核心登录和签到部分已尝试

        # 6. 检查水果并尝试提现
        # 从用户信息中直接获取remaining_fruits
        remaining_fruits_str = user_info.get('remaining_fruits')
        if remaining_fruits_str is not None:
            try:
                remaining_fruits_float = float(remaining_fruits_str)
                # The following log is redundant as _get_user_info already logs current fruit balance.
                # self.log_msg(f"当前剩余水果: {remaining_fruits_float}", logging.INFO)
                if remaining_fruits_float > 0.3:
                    # self.log_msg(f"剩余水果 {remaining_fruits_float} > 0.3，尝试提现0.3。", logging.INFO) # This intent is covered by _withdraw_fruits log
                    withdraw_success = self._withdraw_fruits(self.app_token, "0.3")
                    # if withdraw_success:
                    #     self.log_msg("提现请求已发送成功。", logging.INFO) # This log is handled by _withdraw_fruits
                    # else:
                    #     self.log_msg("提现请求失败或未执行。", logging.WARNING) # This log is handled by _withdraw_fruits
                else:
                    self.log_msg(f"剩余水果 {remaining_fruits_float} <= 0.3，不满足提现条件。", logging.INFO)
            except ValueError:
                self.log_msg(f"无法将 'remaining_fruits' ('{remaining_fruits_str}') 转换为数字。", logging.ERROR)
        else:
            self.log_msg("用户信息中未找到 'remaining_fruits'。", logging.WARNING)
            
        # 登录、签到等主要流程完成
        return True

    def send_notification(self, content, title=None, url=None):
        """发送微信通知
        
        Args:
            content: 通知内容
            title: 通知标题，默认为None
            url: 点击通知后跳转的URL，默认为None
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        if not self.enable_push:
            return False
        
        return self.pusher.send(content, title, url)

def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    # 获取脚本信息
    script_info = get_script_info()
    default_env_prefix = script_info["env_prefix"]
    
    parser = argparse.ArgumentParser(description='小程序登录工具')
    parser.add_argument('--server', help='微信中间服务器地址，格式为IP:PORT或完整URL')
    parser.add_argument('--wxid', help='微信ID')
    parser.add_argument('--appid', help='小程序AppID')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--accounts', help='要处理的账号列表，用换行符分隔多个wxid')
    parser.add_argument('--accounts-file', help='包含要处理的账号列表的文件，每行一个wxid')
    parser.add_argument('--get-mobile', action='store_true', help='获取小程序绑定的手机号')
    parser.add_argument('--token-file', help='指定token保存的文件路径')
    parser.add_argument('--env-prefix', default=default_env_prefix, 
                        help=f'环境变量前缀，默认基于脚本名生成：{default_env_prefix}')
    parser.add_argument('--test', action='store_true', help='测试模式，只打印配置信息不执行操作')
    parser.add_argument('--enable-push', action='store_true', help='启用微信通知推送（默认关闭）')
    parser.add_argument('--wxpush-token', help='WxPusher的AppToken')
    parser.add_argument('--wxpush-uid', help='接收通知的用户ID')
    return parser.parse_args()

def get_envs(prefix: str) -> Dict[str, str]:
    """获取指定前缀的环境变量"""
    result = {}
    for key, value in os.environ.items():
        if key.startswith(prefix + '_'):
            result[key] = value
    return result

def main() -> bool:
    """主函数，用于命令行调用"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置日志级别
    log_level = logging.DEBUG if args.debug else logging.INFO
    
    # 环境变量前缀
    env_prefix = args.env_prefix
    
    # 获取配置参数 - 优先使用命令行参数，其次使用环境变量
    # server变量名固定为PHONECODE_SERVER，这是微信中间服务器地址，用于处理微信登录
    server = args.server or os.environ.get('PHONECODE_SERVER')
    # APPID直接使用命令行参数或类中的DEFAULT_APPID，不从环境变量读取
    appid = args.appid
    token_file = args.token_file or os.environ.get(f'{env_prefix}_TOKEN_FILE')
    
    # 微信推送配置 - 默认关闭
    enable_push = args.enable_push or (os.environ.get('WXPUSH_ENABLE', '').lower() == 'true')
    wxpush_token = args.wxpush_token or os.environ.get('WXPUSH_TOKEN')
    wxpush_uid = args.wxpush_uid or os.environ.get('WXPUSH_UID')
    
    # 验证必要参数
    if not server:
        print(f'错误：未提供微信中间服务器地址，请使用--server参数或设置PHONECODE_SERVER环境变量')
        return False
    
    # appid不需要验证，因为已有DEFAULT_APPID作为默认值
    
    # 处理账号 - 支持多种格式
    wxids = []
    
    # 1. 命令行参数指定的多账号
    if args.accounts:
        wxids = [wxid.strip() for wxid in args.accounts.split('\n') if wxid.strip()]
    
    # 2. 文件中的多账号
    elif args.accounts_file and os.path.exists(args.accounts_file):
        with open(args.accounts_file, 'r', encoding='utf-8') as f:
            wxids = [line.strip() for line in f if line.strip()]
    
    # 3. 命令行参数指定的单账号
    elif args.wxid:
        wxids = [args.wxid]
    
    # 4. 环境变量 - 支持多种分隔符
    else:
        # 只使用TXX_WXID环境变量
        env_wxid = os.environ.get('TXX_WXID')
        
        if env_wxid:
            # 优先使用换行符分隔
            if '\n' in env_wxid:
                wxids = [wxid.strip() for wxid in env_wxid.split('\n') if wxid.strip()]
            # 其次考虑青龙常用分隔符 & 或 @
            elif '&' in env_wxid:
                wxids = [wxid.strip() for wxid in env_wxid.split('&') if wxid.strip()]
            elif '@' in env_wxid:
                wxids = [wxid.strip() for wxid in env_wxid.split('@') if wxid.strip()]
            else:
                wxids = [env_wxid]
    
    if not wxids:
        print(f'错误：未提供任何WXID，请使用--wxid参数或设置TXX_WXID环境变量')
        return False
    
    # 测试模式 - 只打印配置
    if args.test:
        print("\n===== 配置信息 =====")
        print(f"微信中间服务器地址: {server}")
        print(f"AppID: {appid}")
        print(f"账号数量: {len(wxids)}")
        print(f"账号列表: {wxids}")
        print(f"Token文件: {token_file or '默认路径'}")
        print(f"调试模式: {'开启' if args.debug else '关闭'}")
        print(f"获取手机号: {'是' if args.get_mobile else '否'}")
        print(f"微信推送: {'开启' if enable_push else '关闭'}")
        if enable_push:
            print(f"WxPusher Token: {wxpush_token or DEFAULT_WXPUSH_TOKEN}")
            print(f"WxPusher UID: {wxpush_uid or DEFAULT_WXPUSH_UID}")
        print("\n环境变量:")
        for k, v in get_envs(env_prefix).items():
            print(f"  {k}: {v}")
        print("===================")
        return True
    
    print(f'准备处理 {len(wxids)} 个账号...')
    
    # 多账号处理
    success_count = 0
    failed_wxids = []
    error_wxids = []
    
    for i, wxid in enumerate(wxids):
        try:
            print(f'正在处理第 {i+1}/{len(wxids)} 个账号: {wxid}')
            client = PhonecodeClient(
                server, wxid, appid, log_level, token_file, 
                enable_push=False  # 禁用账号处理过程中的推送，只在最后发送汇总
            )
            client.log_msg(f"开始处理账号: {wxid}", logging.INFO)
            
            if client.login_process():
                client.log_msg(f"账号 {wxid} 处理成功", logging.INFO)
                success_count += 1
            else:
                client.log_msg(f"账号 {wxid} 处理失败", logging.ERROR)
                failed_wxids.append(wxid)
            
            # 添加账号处理之间的延迟
            if i < len(wxids) - 1:
                time.sleep(5)
                
        except Exception as e:
            print(f"处理账号 {wxid} 时发生错误: {str(e)}")
            error_wxids.append(wxid)
    
    # 脚本运行完成后发送一次总结通知
    if enable_push:
        try:
            pusher = WxPusher(wxpush_token, wxpush_uid)
            
            # 构建详细的汇总信息
            script_name = get_script_info()['script_name']
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            content = f"小程序登录任务完成\n\n" \
                     f"脚本: {script_name}\n" \
                     f"执行时间: {current_time}\n" \
                     f"总账号数: {len(wxids)}\n" \
                     f"登录成功: {success_count}\n"
            
            if failed_wxids:
                content += f"\n登录失败账号({len(failed_wxids)}):\n" + "\n".join(failed_wxids[:10])
                if len(failed_wxids) > 10:
                    content += f"\n... 等共{len(failed_wxids)}个账号"
            
            if error_wxids:
                content += f"\n出错账号({len(error_wxids)}):\n" + "\n".join(error_wxids[:10])
                if len(error_wxids) > 10:
                    content += f"\n... 等共{len(error_wxids)}个账号"
            
            # 发送汇总通知
            pusher.send(
                content,
                f"【{script_name}】任务完成 - {success_count}/{len(wxids)}"
            )
        except Exception as e:
            print(f"发送汇总通知失败: {str(e)}")
    
    print(f'处理完成! 成功: {success_count}/{len(wxids)}')
    return success_count > 0

# 作为模块导入时不执行主函数
# 作为模块独立运行时的入口点
def cli_entry_point() -> None:
    """命令行入口点，与核心逻辑分离"""
    try:
        result = main()
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    cli_entry_point()